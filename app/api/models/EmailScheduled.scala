package api.models

import java.time.LocalDateTime
import api.AppConfig
import io.smartreach.esp.api.emails.{IEmailAddress, InternetMessageId}
import io.smartreach.esp.utils.email.EmailReplyBounceType
import play.api.libs.json.JsValue
import scalikejdbc.DB
import scalikejdbc._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.SRLogger
import utils.dbutils.SQLUtils
import utils.email.{FolderType, SendEmailResponse}
import utils.security.EncryptionService

import scala.concurrent.blocking
import scala.util.Try

case class EmailToBeScheduled(
                               sender_email_account_id: Long,
                               from_email: String,
                               from_name: String,
                               receiver_email_account_id: Long,
                               to_email: String,
                               to_name: String,
                               account_id:Long,
                               htmlBody: String,
                               textBody: String,
                               subject: String,
                               template_id: Long,
                               is_reply: Boolean,
                               warmup_email_account_id: Long,
                               email_thread_id: String,
                               in_reply_to_header: Option[String],
                               references_header: Option[String]
                             )

case class EmailAccountForScheduling(
                                      id: EmailAccountId,
                                      sender_email: String,
                                      account_id: AccountId,
                                    )

case class EmailScheduledAfterSaving(
                                      email_scheduled_id: Long,
                                      sender_email_account_id: Long,
                                      receiver_email_account_id: Long
                                    )

case class EmailScheduled(
                           id: Long,
                           sender_email_account_id: Long,
                           from_email: String,
                           from_name: String,
                           receiver_email_account_id: Long,
                           to_email: String,
                           to_name: String,
                           account_id: Long,
                           body: String,
                           subject: String,
                           template_id: Long,
                           is_reply: Boolean,
                           message_id: Option[String],
                           warmup_email_account_id: Long,
                           email_thread_id: String,
                           in_reply_to_header: Option[String],
                           references_header: Option[String],
                           sent_at: Option[LocalDateTime],
                           scheduled_at: Option[LocalDateTime]
                         )

case class EmailSendDetail(
                            id: Long,
                            subject: String,
                            body: String,
                            text_body: String,
                            is_reply: Boolean,
                            account_id: Long,

                            sender_email_account_id: Long,
                            receiver_email_account_id: Long,
                            service_provider: EmailServiceProvider,

                            send_settings: EmailSettings.EmailSendSettings,

                            from_email: String,
                            from_name: String,
                            to_emails: Seq[IEmailAddress],
                            reply_to_email: Option[String],
                            reply_to_name: Option[String],

                            message_id_suffix: String,
                            in_reply_to_header: Option[String],
                            references_header: Option[String],
                            sender_email_setting_paused_till: Option[LocalDateTime],
                          )

object EmailScheduled {

  def fromDb(rs: WrappedResultSet): EmailScheduled = {

    EmailScheduled(
      id = rs.long("id"),
      subject = rs.string("subject"),
      body = rs.string("body"),
      sender_email_account_id = rs.long("sender_email_account_id"),
      from_email = rs.string("from_email"),
      from_name = rs.string("from_name"),
      receiver_email_account_id = rs.long("receiver_email_account_id"),
      to_email = rs.string("to_email"),
      to_name = rs.string("to_name"),
      account_id = rs.long("account_id"),
      template_id = rs.long("template_id"),
      is_reply = rs.boolean("is_reply"),
      message_id = rs.stringOpt("message_id"),
      warmup_email_account_id = rs.long("warmup_email_account_id"),
      email_thread_id = rs.string("email_thread_id"),
      in_reply_to_header = rs.stringOpt("in_reply_to_header"),
      references_header = rs.stringOpt("references_header"),
      sent_at = rs.localDateTimeOpt("sent_at"),
      scheduled_at = rs.localDateTimeOpt("scheduled_at")
    )
  }


  def saveEmailsToBeScheduled(
                               emailsToBeScheduled: EmailToBeScheduled,
                               Logger: SRLogger
                             ): Try[Seq[EmailScheduledAfterSaving]] = {

    Logger.info(s"saveEmailsToBeScheduled got emails: ${emailsToBeScheduled}")

    val saved =
      blocking {
        Try {
          DB localTx { implicit session =>

            val insertedEmailScheduledListAfterSave = _insertMultipleForWarmup(emailsToBeScheduled = Vector(emailsToBeScheduled))

            insertedEmailScheduledListAfterSave

          }
        }
      }

    if (saved.isSuccess) {
      Logger.info(s"saveEmailsToBeScheduled saved:: emailScheduledId: ${saved.get.map(_.email_scheduled_id)}")
    }

    saved
  }


  def _insertMultipleForWarmup(emailsToBeScheduled: Vector[EmailToBeScheduled]): Seq[EmailScheduledAfterSaving] = {
    DB localTx { implicit session =>

      if (emailsToBeScheduled.isEmpty) Seq()
      else {

        var parameters = List[Any]()


        val valuePlaceholder = emailsToBeScheduled.map(email => {


          parameters = parameters ::: List(

            email.account_id,
            email.sender_email_account_id,
            email.from_email,
            email.from_name,
            email.receiver_email_account_id,
            email.to_email,
            email.to_name,
            email.htmlBody,
            email.textBody,
            email.subject,
            email.template_id,
            email.is_reply,
            email.warmup_email_account_id,
            email.email_thread_id,
            email.in_reply_to_header,
            email.references_header

          )

          s"""
            (
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?

            )

          """
        }).mkString(", ")

        SQL(
          s"""
          INSERT INTO emails_scheduled
          (

           account_id,
           sender_email_account_id,
           from_email,
           from_name,
           receiver_email_account_id,
           to_email,
           to_name,
           body,
           text_body,
           subject,
           template_id,
           is_reply,
           warmup_email_account_id,
           email_thread_id,
           in_reply_to_header,
           references_header

          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING *;
        """)
          .bind(parameters: _*)
          .map(rs => {
            EmailScheduledAfterSaving(
              email_scheduled_id = rs.long("id"),
              sender_email_account_id = rs.long("id"),
              receiver_email_account_id = rs.long("id")
            )
          })
          .list()
          .apply()

      }
    }

  }


  def getTotalNewEmailsOrRepliesSentToday(
    emailAccountId: Long,
    isReply: Boolean,
    accountId: Long,
  ): Try[Option[Int]] = Try {

    // isReply:
    // false - when sending a reply to a received warmup email
    // true  - when sending a new warmup email

    DB readOnly { implicit session =>

      sql"""
          SELECT
            count(*) AS total_sent_today
          FROM
            emails_scheduled
          WHERE
            sender_email_account_id = $emailAccountId
            AND account_id = $accountId
            AND sent_at >= CURRENT_DATE::TIMESTAMPTZ
            AND is_reply = $isReply
         """
        .map { rs =>

          rs.int("total_sent_today")

        }
        .single()
        .apply()

    }

  }

  def getTotalNewEmailsOrRepliesScheduledToday(
    emailAccountId: Long,
    isReply: Boolean,
    accountId: Long,
  ): Try[Option[Int]] = Try {

    // isReply:
    // false - when sending a reply to a received warmup email
    // true  - when sending a new warmup email

    DB readOnly { implicit session =>

      sql"""
          SELECT
            count(*) AS total_scheduled_today
          FROM
            emails_scheduled
          WHERE
            sender_email_account_id = $emailAccountId
            AND account_id = $accountId
            AND scheduled_at >= CURRENT_DATE::TIMESTAMPTZ
            AND is_reply = $isReply
         """
        .map { rs =>

          rs.int("total_scheduled_today")

        }
        .single()
        .apply()

    }

  }


  def getScheduleDetailsForSending(emailScheduledId: Long): Try[Option[EmailSendDetail]] = Try {
    DB readOnly { implicit session =>


      sql"""

          SELECT
          s.id,
          s.subject,
          s.body,
          s.text_body,
          s.is_reply,
          s.in_reply_to_header,
          s.references_header,

          s.account_id,

          eas.id AS sender_email_account_id,
          eas.service_provider,

          eas.smtp_username,
          eas.smtp_host,
          eas.smtp_port,
          eas.smtp_password,

          eas.oauth2_refresh_token_enc,
          eas.oauth2_access_token_enc,
          eas.oauth2_access_token_expires_at,

          eas.email AS from_email,
          eas.sender_name AS from_name,

          eas.email as reply_to_email,
          eas.sender_name as reply_to_name,

          eas.message_id_suffix,
          eas.paused_till AS sender_email_setting_paused_till,

          ear.id AS receiver_email_settings_id,
          ear.email as to_email,
          ear.sender_name as to_name

          FROM emails_scheduled s
          LEFT JOIN email_accounts eas ON s.sender_email_account_id = eas.id
          LEFT JOIN email_accounts ear ON s.receiver_email_account_id = ear.id
          WHERE s.id = $emailScheduledId
          --AND s.sent IS FALSE
          LIMIT 1
      """


        .map(rs => {

          val key = AppConfig.emailAccountEncryptionKey

          val service_provider = EmailServiceProvider.withName(rs.string("service_provider")).get

          val send_settings: EmailSettings.EmailSendSettings = service_provider match {

            case api.models.EmailServiceProvider.GOOGLE_WORKSPACE
                 | api.models.EmailServiceProvider.MICROSOFT_365
                 | api.models.EmailServiceProvider.OTHER =>

              val smtp_password = rs.string("smtp_password")
              val decryptedSMTPPassword = EncryptionService.decrypt(key, smtp_password)

              EmailSettings.SmtpEmailAccount(
                smtp_username = rs.string("smtp_username"),
                smtp_host = rs.string("smtp_host"),
                smtp_port = rs.int("smtp_port"),
                smtp_password = decryptedSMTPPassword,
              )

            case api.models.EmailServiceProvider.MICROSOFT_365_API =>

              val oauth2_refresh_token = EncryptionService.decrypt(
                key = key, encryptedValue = rs.string("oauth2_refresh_token_enc")
              )

              val oauth2_access_token = EncryptionService.decrypt(
                key = key, encryptedValue = rs.string("oauth2_access_token_enc")
              )

              val oauth2_access_token_expires_at = rs.jodaDateTime("oauth2_access_token_expires_at")

              EmailSettings.OAuthTokens(
                oauth2_refresh_token = oauth2_refresh_token,
                oauth2_access_token = oauth2_access_token,
                oauth2_access_token_expires_at = oauth2_access_token_expires_at
              )
          }

          EmailSendDetail(
            id = rs.long("id"),

            subject = rs.string("subject"),
            body = rs.string("body"),
            text_body = rs.string("text_body"),
            sender_email_account_id = rs.long("sender_email_account_id"),
            is_reply = rs.boolean("is_reply"),
            account_id = rs.long("account_id"),

            receiver_email_account_id = rs.long("receiver_email_settings_id"),

            service_provider = service_provider,

            send_settings = send_settings,

            from_email = rs.string("from_email"),
            from_name = rs.string("from_name"),
            reply_to_email = rs.stringOpt("reply_to_email"),
            reply_to_name = rs.stringOpt("reply_to_name"),

            to_emails = Seq(IEmailAddress(
              name = rs.stringOpt("to_name"),
              email = rs.string("to_email")
            )),

            message_id_suffix = rs.string("message_id_suffix"),
            in_reply_to_header = rs.stringOpt("in_reply_to_header"),
            references_header = rs.stringOpt("references_header"),
            sender_email_setting_paused_till = rs.localDateTimeOpt("sender_email_setting_paused_till")
          )
        }
        )
        .single()
        .apply()

    }
  }


  def find(id: Long, Logger: SRLogger): Try[Option[EmailScheduled]] = Try {

    Logger.info(s"called EmailScheduled.id:: $id")

    val whereClause =
      sqls"""
          WHERE
            es.id = $id
            AND es.sender_email_account_id IS NOT NULL
            AND es.receiver_email_account_id IS NOT NULL
        """

    __findEmailScheduledSQL(whereClause = whereClause)
      .headOption
  }


  def __findEmailScheduledSQL(whereClause: SQLSyntax): Seq[EmailScheduled] = {
    DB readOnly { implicit session =>

      sql"""
        SELECT
            es.id,
            es.subject,
            es.body,
            es.sender_email_account_id,
            es.from_email,
            es.from_name,
            es.receiver_email_account_id,
            es.to_email,
            es.to_name,
            es.account_id,
            es.template_id,
            es.is_reply,
            es.scheduled_at,
            es.sent_at,
            es.message_id,
            es.warmup_email_account_id,
            es.email_thread_id,
            es.in_reply_to_header,
            es.references_header
          FROM emails_scheduled es
          $whereClause
      """
        .map(fromDb)
        .list()
        .apply()

    }

  }


  def isSent(emailSentId: Long,
             data: SendEmailResponse,
             Logger: SRLogger
            ): Try[EmailScheduled] = Try {
    DB localTx { implicit session =>

      val markedSent =
        sql"""
          UPDATE emails_scheduled
          SET
            message_id = ${data.messageId.id},
            sent_at = now(),
            sent = true
          WHERE id = $emailSentId
          RETURNING *;
      """
          .map(EmailScheduled.fromDb)
          .single()
          .apply()
          .get

      markedSent



    }
  }


  def updateRepliedStatus(senderEmailAccountId: Long, receiverEmailAccountId: Long, Logger: SRLogger
                         ): Try[Int] = Try {
    DB localTx { implicit session =>
      sql"""
          UPDATE emails_scheduled
          SET
            replied_at = now(),
            replied = true
          WHERE sender_email_account_id = $senderEmailAccountId
            AND receiver_email_account_id = $receiverEmailAccountId
            AND is_reply = FALSE
        """
        .update()
        .apply()

    }
  }

  def updateBounceStatus(
                          emailScheduledId: Long,
                          bouncedReplyBody: String,
                          bouncedReplySubject: String,
                          bouncedReplyHeaders: JsValue,
                          bounceType: EmailReplyBounceType
                        ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE
            emails_scheduled
          SET
            has_email_bounced = TRUE,
            bounce_type = ${bounceType.toString},
            bounced_reply_body = $bouncedReplyBody,
            bounced_reply_subject = $bouncedReplySubject,
            bounced_reply_headers = to_json(${bouncedReplyHeaders.toString}::jsonb),
            landed_folder_type = ${FolderType.BOUNCED.toString}::folder_type_enum,
            landing_check_performed_at = now(),
            landing_check_tried_count = COALESCE(landing_check_tried_count, 0) + 1
          WHERE
            id = $emailScheduledId;
         """
        .update()
        .apply()

    }

  }

  def incrementLandingCheckTriedCount(
                                       emailScheduledId: Long,
                                     ): Try[Int] = Try {

    // TODO: should we set the landing_check_failed status at first check or only after second check?

    DB autoCommit { implicit session =>
      sql"""
          UPDATE
            emails_scheduled
          SET
            landed_folder_type = ${FolderType.LANDING_CHECK_FAILED.toString}::folder_type_enum,
            landing_check_performed_at = now(),
            landing_check_tried_count = COALESCE(landing_check_tried_count, 0) + 1
          WHERE
            id = $emailScheduledId;
         """
        .update()
        .apply()
    }
  }

  def getLandingCheckTriedCount(
                                 emailScheduledId: Long,
                               ): Try[Option[Int]] = Try {
    DB readOnly { implicit session =>
      sql"""
          SELECT
            landing_check_tried_count
          FROM
            emails_scheduled
          WHERE
            id = $emailScheduledId;
         """
        .map(rs => rs.intOpt("landing_check_tried_count").getOrElse(0))
        .single()
        .apply()
    }
  }

  def updateIsScheduledForSending(emailScheduledId: Long, Logger: SRLogger): Int = {
    blocking {
      DB autoCommit { implicit session =>
        Logger.info(s"updateIsScheduledForSending is_scheduled_for_sending: $emailScheduledId")

        sql"""
            UPDATE emails_scheduled
            SET is_scheduled_for_sending = false
            WHERE id = $emailScheduledId
           """
          .update()
          .apply()
      }
    }
  }

  def updateIsScheduledForLandingCheck(emailScheduledId: Long, Logger: SRLogger): Int = {
    blocking {
      DB autoCommit { implicit session =>
        Logger.info(s"updateIsScheduledForLandingCheck is_scheduled_for_landing_check: $emailScheduledId")

        sql"""
            UPDATE emails_scheduled
            SET is_scheduled_for_landing_check = false
            WHERE id = $emailScheduledId
           """
          .update()
          .apply()
      }
    }
  }

  def updateIsScheduledForReply(emailScheduledId: Long, Logger: SRLogger): Int = {
    blocking {
      DB autoCommit { implicit session =>
        Logger.info(s"updateIsScheduledForReply is_scheduled_for_reply: $emailScheduledId")

        sql"""
            UPDATE emails_scheduled
            SET is_scheduled_for_reply = false
            WHERE id = $emailScheduledId
           """
          .update()
          .apply()
      }
    }
  }

  /* originalLandedFolder => email actually landed in
   * landedFolder => after moving (ex: if landed in spam we will move it inbox)
   * if email not originalLanded in spam than both will be same
   */
  def updateLandingFolderAndMessageIds(
                                        emailScheduledId: Long,
                                        originalLandedFolder: String,
                                        landedFolder: String,
                                        landedFolderType: FolderType.Value,
                                        messageIdOpt: Option[InternetMessageId],
                                        Logger: SRLogger
                                      ): Try[Int] = blocking {
    Try {
      DB autoCommit { implicit session =>

        val updateClause = if (messageIdOpt.isDefined) {

          sqls"""
                message_id = ${messageIdOpt.get.id},
              """
        } else {

          sqls""""""
        }

        sql"""
            UPDATE emails_scheduled
            SET
              $updateClause
              original_landed_folder = $originalLandedFolder,
              landed_folder = $landedFolder,
              landed_folder_type = ${landedFolderType.toString}::folder_type_enum
            WHERE id = $emailScheduledId
          """
          .update()
          .apply()

      }
    }
  }


  def getAllEmailScheduledReadyForSending(): Try[Seq[Long]] = Try {

    DB autoCommit { implicit session =>

      sql"""
           UPDATE emails_scheduled es_rows SET
             is_scheduled_for_sending = TRUE,
             is_scheduled_for_sending_at = now()

           FROM
              (
                 SELECT DISTINCT ON (es.id)
                   es.id
                 FROM emails_scheduled es

                 -- this query will fetch both reply and primary rows which sent = false
                 WHERE sent = FALSE
                   AND replied = FALSE
                   AND is_scheduled_for_sending = FALSE
                   AND sender_email_account_id IS NOT NULL
                   AND receiver_email_account_id IS NOT NULL
                   ORDER BY es.id, es.is_scheduled_for_sending ASC

              ) filtered_email_scheduled_rows

           WHERE es_rows.id = filtered_email_scheduled_rows.id
           RETURNING filtered_email_scheduled_rows.*
            ;
             """
        .map(rs => (rs.long("id")))
        .list()
        .apply()
    }
  }


  def getAllSentEmailsReadyForCheckLandingFolder(): Try[Seq[Long]] = Try {

    val stopLandingCheckAfterTries = AppConfig.stopLandingCheckAfterTries

    DB autoCommit { implicit session =>

      sql"""
           UPDATE emails_scheduled es_rows SET
             is_scheduled_for_landing_check = TRUE,
             is_scheduled_for_landing_check_at = now()

           FROM
              (
                 SELECT DISTINCT ON (es.id)
                   es.id
                 FROM
                   emails_scheduled es
                   JOIN email_accounts AS sea ON es.sender_email_account_id = sea.id
                   JOIN email_accounts AS rea ON es.receiver_email_account_id = rea.id
                   JOIN accounts AS sacc ON sea.account_id = sacc.id
                   JOIN accounts AS racc ON rea.account_id = racc.id

                 WHERE
                   --this query will fetch all sent emails for landing check (both replies and primary)
                   -- and sent_at 1 hour older to avoid checking bounced emails again and again
                   sent = TRUE
                   AND sent_at < now() - interval '1 hour'
                   AND replied = FALSE
                   AND is_scheduled_for_sending = FALSE
                   AND is_scheduled_for_landing_check = FALSE
                   AND original_landed_folder IS NULL
                   AND landed_folder IS NULL
                   AND (has_email_bounced IS NULL OR NOT has_email_bounced)
                   AND COALESCE(landing_check_tried_count, 0) < $stopLandingCheckAfterTries
                   AND (landing_check_performed_at IS NULL OR landing_check_performed_at < now() - interval '1 day')
                   AND (sacc.account_paused IS NULL OR sacc.account_paused = FALSE)
                   AND (racc.account_paused IS NULL OR racc.account_paused = FALSE)
                   AND (sea.paused_till IS NULL OR sea.paused_till < now())
                   AND (rea.paused_till IS NULL OR rea.paused_till < now())
                   ORDER BY es.id, es.is_scheduled_for_reply_at ASC

              ) filtered_email_scheduled_rows

           WHERE es_rows.id = filtered_email_scheduled_rows.id
           RETURNING filtered_email_scheduled_rows.*
            ;
             """
        .map(rs => (rs.long("id")))
        .list()
        .apply()
    }
  }


  def getAllEmailScheduledReadyForReply(): Try[List[Long]] = Try {
    DB readOnly  { implicit session =>

      sql"""
                 SELECT DISTINCT ON (es.id)
                   es.id
                 FROM
                   emails_scheduled es
                   JOIN accounts AS acc ON es.account_id = acc.id

                 -- this query will fetch ready for reply email scheduled rows
                 -- only landing check done and replied false and sent true rows
                 WHERE is_reply = FALSE
                   AND (acc.account_paused IS NULL OR acc.account_paused = FALSE)
                   AND sent = TRUE
                   AND replied = FALSE

                   -- If receiver_limit_reached_for_reply is true,
                   -- it means that the receiver has reached the limit of replies.
                   -- So, we should not schedule any more replies from this receiver.
                   -- Its fine even if all sent emails do not get replies
                   AND (
                      receiver_limit_reached_for_reply IS NULL
                      OR receiver_limit_reached_for_reply = FALSE
                   )

                   AND is_scheduled_for_sending = FALSE
                   AND is_scheduled_for_landing_check = FALSE
                   AND is_scheduled_for_landing_check_at < now() - interval '30 minutes'
                   AND is_scheduled_for_reply = FALSE
                   AND is_scheduled_for_reply_at IS NULL
                   AND original_landed_folder IS NOT NULL
                   AND landed_folder IS NOT NULL
                   AND sender_email_account_id IS NOT NULL
                   AND receiver_email_account_id IS NOT NULL
                   ORDER BY es.id ASC
         ;
             """
        .map(rs => rs.long("id"))
        .list()
        .apply()
    }
  }



  def updateScheduledForReply(emailScheduledIdsToBeUpdated: List[Long]): Try[List[Long]] = Try {

    if (emailScheduledIdsToBeUpdated.isEmpty) {

      List()

    } else {
      DB autoCommit { implicit session =>

        sql"""
           UPDATE emails_scheduled es SET
             is_scheduled_for_reply = TRUE,
             is_scheduled_for_reply_at = now()
           WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIdsToBeUpdated)}
           RETURNING es.id;
        """
          .map(rs => rs.long("id"))
          .list()
          .apply()
      }
    }
  }


  /* delete from emails_scheduled which are not sent yet*/
  def deleteUnsentByEmailAccountId(emailAccountId: Long): Try[Seq[Long]] = blocking {
    Try {
      DB autoCommit { implicit session =>

        sql"""
          DELETE FROM emails_scheduled
          WHERE sender_email_account_id = $emailAccountId
            AND sent = false
          RETURNING id;
        """
          .map(rs => rs.long("id"))
          .list()
          .apply()

      }

    }
  }
}
