package api

import com.typesafe.config.{Config, ConfigFactory}
import play.api.Logger
import io.smartreach.esp.api.microsoftOAuth.MicrosoftOAuthSettings
import api.models.MsClientIdVersion
import utils.cronjobs.WarmupTopicAndPrompt



object AppConfig {

  val config: Config = ConfigFactory.load()

  val appName = "WarmupHero"
  val dashboardDomain = config.getString("application.domain")
  val apiDomain = config.getString("application.apiDomain")
  val srBaseDomain = "http://localhost:9000"
  val productionDashboardDomain = "http://localhost:3002"

  val srApiKey: String = config.getString("smartreach.apiKey")

  val emailAccountUrl = s"$productionDashboardDomain/dashboard/email_accounts"

  // NOTE: used for checking request header, must not have http / https
  val rootDomain = "localhost"

  val isProd = config.getBoolean("application.isprod")

  val AUTO_LOGIN_ACCESS_TOKEN_VALID_FOR_SECONDS = 60

  val defaultFinderCredits = 100

//  val applicationEncryptionKey = config.getString("application.encryptionKey")

  // val redisKeyPrefix = config.getString("redis.prefix")

  val emailAccountEncryptionKey = config.getString("application.emailaccountencryptionKey")

  val cronScheduleEmailIntervalInSeconds = 60

  val landingCheckErrorLimit = 5

  val stopLandingCheckAfterTries = 2

  val mailgunHost = config.getString("mailgun.host")
  val mailgunLogin = config.getString("mailgun.login")
  val mailgunPassword = config.getString("mailgun.password")
  val mailgunDomain = config.getString("mailgun.domain")
  val mailgunApiKey = config.getString("mailgun.apiKey")

  val adminEmail = config.getString("mailgun.adminEmail")
  val adminName = config.getString("mailgun.adminName")


  def getMicrosoftOAuthSettings(isZapmailFlow: Boolean, version: MsClientIdVersion) = {

    val clientID = version match {

      case MsClientIdVersion.V1 => config.getString("microsoft.clientID")

      case MsClientIdVersion.V2 => config.getString("microsoft.clientIDV2")

    }

    val clientSecret = version match {

      case MsClientIdVersion.V1 => config.getString("microsoft.clientSecret")

      case MsClientIdVersion.V2 => config.getString("microsoft.clientSecretV2")

    }

    val microsoftOAuthSettings = MicrosoftOAuthSettings(
      authorizationURL = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
      accessTokenURL = "https://login.microsoftonline.com/common/oauth2/v2.0/token",
      redirectRoute = "/oauth/redirect",
      clientID = clientID,
      clientSecret = clientSecret,
      scope = "offline_access user.read mail.send mail.readwrite",
      redirectURL = dashboardDomain + "/oauth/redirect"
    )

    if (isZapmailFlow) {
      microsoftOAuthSettings.copy(
        redirectURL = "https://api.warmuphero.com/api/v1/email_accounts/oauth/kylpfqzpgxkmnrtzoftbxsrthphqfy/code"
      )
    } else {
      microsoftOAuthSettings
    }

  }

  object EmailHealthCheck {

    val emailHealthCheckSubject = "SPF, DKIM, and DMARC check for WarmupHero.com"
    val emailHealthCheckTextBody = "This is to verify SPF, DKIM, and DMARC."

    private val receiverEmailSettingId_prod: Long = 6 // <EMAIL>

    val maxRetryCount = 5

    def getReceiverEmailSettingIdForEmailHealthCheck: Long = {

      receiverEmailSettingId_prod

    }

  }

  def waitForMinutesBeforeNextSchedule: Int = {

    val min = -12
    val max = 12

    // Calculate the range size (inclusive)
    val range = max - min + 1

    // Generate a random number within the range [0, range) and then shift it
    val randomNumber = scala.util.Random.nextInt(range) + min

    // 30 (+/-) 12
    30 + randomNumber

  }

  /**
    * 21-Sep-2024
    *
    * Changed the default warm_up_emails_per_day from 30 to 10 to prevent oversending.
    */
  val defaultWarmUpEmailsPerDay = 10

  private val warmupTopicsAndPrompts: List[WarmupTopicAndPrompt] = List(

    WarmupTopicAndPrompt(topic = "Implementing AI in Business Operations", prompt = "Generate a professional email targeted to a technology company executive. The subject should be 'Unlock Efficiency: Integrating AI into Your Daily Operations'. The body should introduce the benefits of AI implementation, share a case study from a similar IT firm, offer a free consultation, and include a call-to-action to schedule a meeting."),

    WarmupTopicAndPrompt(topic = "Cybersecurity Threats and Solutions", prompt = "Create an email for IT service providers. Use the subject 'Safeguard Your Network: Emerging Cybersecurity Risks in 2025'. The body should outline recent threats like ransomware, explain preventive measures, highlight your expertise in cybersecurity audits, and end with an invitation to a webinar."),

    WarmupTopicAndPrompt(topic = "Cloud Migration Strategies", prompt = "Write an outreach email to software development companies. Subject: 'Seamless Transition: Optimize with Cloud Migration Today'. The body should discuss common migration challenges, benefits of cloud services, provide tips for a smooth process, and propose a personalized strategy session."),

    WarmupTopicAndPrompt(topic = "Telemedicine Adoption Trends", prompt = "Draft an email for hospital administrators. Subject: 'Revolutionize Patient Care: Embrace Telemedicine in 2025'. The body should cover adoption statistics, benefits for remote consultations, regulatory updates, and offer a demo of your telemedicine platform."),

    WarmupTopicAndPrompt(topic = "Drug Development Innovations", prompt = "Generate an email aimed at pharmaceutical researchers. Subject: 'Accelerate Breakthroughs: Latest in AI-Driven Drug Discovery'. The body should explain how AI speeds up development, include success stories, discuss potential collaborations, and invite to a virtual roundtable."),

    WarmupTopicAndPrompt(topic = "Patient Data Privacy Compliance", prompt = "Create an informative email for healthcare clinics. Subject: 'Stay Compliant: Navigating HIPAA Updates for Data Security'. The body should detail recent compliance changes, risks of non-compliance, your services for audits, and a call-to-action for a free assessment."),

    WarmupTopicAndPrompt(topic = "Fintech Integration for Banks", prompt = "Write an email to banking executives. Subject: 'Enhance Services: Integrate Fintech for Competitive Edge'. The body should highlight fintech benefits like mobile banking, case studies from adopters, potential ROI, and suggest a partnership discussion."),

    WarmupTopicAndPrompt(topic = "Risk Management in Investments", prompt = "Draft an email for investment firms. Subject: 'Mitigate Risks: Advanced Strategies for Volatile Markets'. The body should cover current market risks, tools for better management, your advisory services, and an offer for a portfolio review."),

    WarmupTopicAndPrompt(topic = "Sustainable Finance Practices", prompt = "Generate an email targeted to financial institutions. Subject: 'Go Green: Implementing ESG in Your Finance Portfolio'. The body should discuss ESG trends, benefits for long-term growth, regulatory incentives, and invite to a sustainability workshop."),

    WarmupTopicAndPrompt(topic = "E-commerce Optimization", prompt = "Create an email for online retailers. Subject: 'Boost Sales: Proven E-commerce Optimization Techniques'. The body should outline SEO and UX improvements, share retail success metrics, offer optimization tools, and include a CTA for a site audit."),

    WarmupTopicAndPrompt(topic = "Supply Chain Sustainability", prompt = "Write an outreach email to consumer goods manufacturers. Subject: 'Sustainable Supply Chains: Reduce Costs and Impact'. The body should explain eco-friendly practices, cost-saving examples, your consulting services, and propose a supply chain analysis."),

    WarmupTopicAndPrompt(topic = "Personalized Marketing Campaigns", prompt = "Draft an email for apparel brands. Subject: 'Engage Customers: Mastery in Personalized Marketing'. The body should cover data-driven personalization, campaign ROI stories, tools for implementation, and an invitation to a strategy session."),

    WarmupTopicAndPrompt(topic = "Automation in Production Lines", prompt = "Generate an email for manufacturing plant managers. Subject: 'Streamline Production: The Power of Automation'. The body should discuss robotic integration benefits, efficiency gains from case studies, your automation solutions, and offer a factory assessment."),

    WarmupTopicAndPrompt(topic = "Quality Control Innovations", prompt = "Create an email aimed at industrial equipment producers. Subject: 'Elevate Standards: Cutting-Edge Quality Control Tools'. The body should highlight AI for defect detection, reduction in waste examples, training programs, and a CTA for a demo."),

    WarmupTopicAndPrompt(topic = "Energy Efficiency in Operations", prompt = "Write an informative email for chemical manufacturers. Subject: 'Cut Costs: Achieve Energy Efficiency in Manufacturing'. The body should cover energy-saving technologies, ROI calculations, compliance with regulations, and invite to an efficiency audit."),

    WarmupTopicAndPrompt(topic = "Renewable Energy Transitions", prompt = "Draft an email for utility companies. Subject: 'Future-Proof Energy: Shifting to Renewables'. The body should outline transition strategies, government incentives, success stories from peers, and propose a renewable energy consultation."),

    WarmupTopicAndPrompt(topic = "Grid Modernization Challenges", prompt = "Generate an email targeted to electric utilities. Subject: 'Upgrade Your Grid: Solutions for Modern Demands'. The body should discuss smart grid tech, reliability improvements, funding options, and offer a modernization roadmap workshop."),

    WarmupTopicAndPrompt(topic = "Oil & Gas Sustainability Practices", prompt = "Create an outreach email for oil companies. Subject: 'Sustainable Extraction: Balancing Profit and Planet'. The body should explain low-emission methods, regulatory trends, cost-benefit analysis, and invite to a sustainability seminar."),

    WarmupTopicAndPrompt(topic = "Sustainable Building Materials", prompt = "Write an email for construction firms. Subject: 'Build Green: Innovative Sustainable Materials'. The body should cover eco-materials benefits, certification processes, project examples, and offer material sourcing advice."),

    WarmupTopicAndPrompt(topic = "Real Estate Market Trends", prompt = "Draft an email for property developers. Subject: 'Navigate 2025: Key Real Estate Market Insights'. The body should analyze current trends, investment opportunities, risk factors, and propose a market forecast session."),

    WarmupTopicAndPrompt(topic = "BIM Technology Adoption", prompt = "Generate an email aimed at architects. Subject: 'Enhance Designs: Leverage BIM for Efficiency'. The body should discuss Building Information Modeling advantages, collaboration tools, training resources, and a CTA for a demo."),

    WarmupTopicAndPrompt(topic = "Supply Chain Disruptions Management", prompt = "Create an email for logistics managers. Subject: 'Overcome Disruptions: Resilient Supply Chain Strategies'. The body should outline risk mitigation, tech for tracking, case studies, and offer a disruption planning consultation."),

    WarmupTopicAndPrompt(topic = "Electric Vehicle Fleet Integration", prompt = "Write an outreach email to shipping companies. Subject: 'Go Electric: Transform Your Fleet Sustainably'. The body should cover EV benefits, charging infrastructure, cost savings, and invite to an integration workshop."),

    WarmupTopicAndPrompt(topic = "Last-Mile Delivery Optimization", prompt = "Draft an email for ride-sharing firms. Subject: 'Efficient Deliveries: Mastering Last-Mile Logistics'. The body should discuss optimization software, speed improvements, customer satisfaction tips, and propose a strategy review."),

    WarmupTopicAndPrompt(topic = "Guest Experience Personalization", prompt = "Generate an email for hotel managers. Subject: 'Delight Guests: Personalized Hospitality in 2025'. The body should explain data-driven personalization, loyalty program enhancements, examples from top hotels, and offer a personalization audit."),

    WarmupTopicAndPrompt(topic = "Sustainable Tourism Practices", prompt = "Create an informative email for travel agencies. Subject: 'Eco-Tourism Boom: Adopt Sustainable Practices'. The body should cover green initiatives, traveler preferences, certification benefits, and invite to a sustainability training."),

    WarmupTopicAndPrompt(topic = "Event Planning Technology", prompt = "Write an email targeted to event planners. Subject: 'Streamline Events: Tech Tools for Seamless Planning'. The body should highlight virtual planning software, hybrid event trends, efficiency gains, and a CTA for a tool demo."),

    WarmupTopicAndPrompt(topic = "Content Creation Trends", prompt = "Draft an email for film producers. Subject: 'Stay Ahead: 2025 Content Creation Innovations'. The body should discuss AI in scripting, streaming platform shifts, audience engagement strategies, and propose a trend analysis session."),

    WarmupTopicAndPrompt(topic = "Digital Advertising Strategies", prompt = "Generate an email aimed at advertising agencies. Subject: 'Maximize Reach: Effective Digital Ad Campaigns'. The body should cover targeted ads, ROI metrics, platform best practices, and offer a campaign optimization workshop."),

    WarmupTopicAndPrompt(topic = "Gaming Industry Growth", prompt = "Create an outreach email for game developers. Subject: 'Level Up: Capitalizing on Gaming Market Expansion'. The body should analyze growth data, monetization models, esports integration, and invite to a development consultation."),

    WarmupTopicAndPrompt(topic = "Online Learning Platforms", prompt = "Write an email for university administrators. Subject: 'Transform Education: Build Robust Online Platforms'. The body should outline e-learning benefits, student engagement tools, implementation tips, and offer a platform setup demo."),

    WarmupTopicAndPrompt(topic = "Corporate Training Programs", prompt = "Draft an email targeted to HR departments. Subject: 'Upskill Teams: Customized Corporate Training'. The body should discuss skill gap solutions, program customization, ROI examples, and propose a needs assessment."),

    WarmupTopicAndPrompt(topic = "EdTech Integration", prompt = "Generate an informative email for schools. Subject: 'Enhance Classrooms: Integrating EdTech Effectively'. The body should cover tech tools for teaching, teacher training, student outcomes, and invite to an integration workshop."),

    WarmupTopicAndPrompt(topic = "Consulting for Digital Transformation", prompt = "Create an email for consulting firms. Subject: 'Lead Change: Digital Transformation Expertise'. The body should explain transformation frameworks, client success stories, your unique approach, and offer a strategy call."),

    WarmupTopicAndPrompt(topic = "Legal Compliance Updates", prompt = "Write an outreach email to law firms. Subject: 'Stay Compliant: 2025 Legal Regulation Changes'. The body should detail key updates, impact on clients, advisory services, and invite to a compliance seminar."),

    WarmupTopicAndPrompt(topic = "Marketing Automation Tools", prompt = "Draft an email aimed at PR agencies. Subject: 'Automate Success: Advanced Marketing Tools'. The body should highlight automation benefits, campaign efficiency, integration examples, and a CTA for a tool trial."),

    WarmupTopicAndPrompt(topic = "Precision Farming Techniques", prompt = "Generate an email for farmers. Subject: 'Harvest More: Precision Farming Innovations'. The body should discuss sensor tech, yield improvements, cost reductions, and offer a farm tech assessment."),

    WarmupTopicAndPrompt(topic = "Food Safety Standards", prompt = "Create an informative email for food processors. Subject: 'Ensure Safety: Updated Food Production Standards'. The body should cover regulations, traceability systems, audit preparation, and invite to a safety workshop."),

    WarmupTopicAndPrompt(topic = "Agribusiness Supply Chain", prompt = "Write an email targeted to agribusinesses. Subject: 'Optimize Chains: Efficient Agribusiness Logistics'. The body should explain supply optimization, risk management, tech integrations, and propose a chain analysis."),

    WarmupTopicAndPrompt(topic = "Fundraising Strategies", prompt = "Draft an email for charity directors. Subject: 'Boost Donations: Effective Fundraising Tactics'. The body should outline digital campaigns, donor engagement, success metrics, and offer a fundraising plan review."),

    WarmupTopicAndPrompt(topic = "Public Policy Advocacy", prompt = "Generate an outreach email to NGOs. Subject: 'Influence Change: Mastering Policy Advocacy'. The body should discuss advocacy tools, coalition building, impact stories, and invite to an advocacy training."),

    WarmupTopicAndPrompt(topic = "Grant Management Best Practices", prompt = "Create an email aimed at government agencies. Subject: 'Streamline Grants: Efficient Management Solutions'. The body should cover tracking software, compliance tips, efficiency gains, and a CTA for a management demo."),

    WarmupTopicAndPrompt(topic = "Waste Management Innovations", prompt = "Write an email for waste companies. Subject: 'Go Zero-Waste: Innovative Recycling Solutions'. The body should explain new tech, environmental benefits, cost savings, and offer a waste audit consultation."),

    WarmupTopicAndPrompt(topic = "Personal Fitness Program Design", prompt = "Draft an informative email for gyms. Subject: 'Customize Fitness: Tailored Programs for Clients'. The body should discuss personalization trends, health outcomes, app integrations, and invite to a program design workshop."),

    WarmupTopicAndPrompt(topic = "Repair Service Efficiency", prompt = "Generate an email targeted to maintenance firms. Subject: 'Faster Fixes: Optimize Your Repair Services'. The body should cover scheduling tools, customer satisfaction, efficiency tips, and propose a service optimization session."),

    WarmupTopicAndPrompt(topic = "AI in Healthcare Diagnostics", prompt = "Create an email for medical professionals. Subject: 'Revolutionize Diagnostics: Harness AI for Better Outcomes'. The body should explain AI applications in diagnostics, successful case studies, and offer a demo of AI-powered diagnostic tools."),

    WarmupTopicAndPrompt(topic = "IoT in Smart Homes", prompt = "Write an email for smart home companies. Subject: 'Future of Living: Smart Homes Powered by IoT'. The body should discuss the convenience of IoT, market trends, benefits for homeowners, and suggest a partnership opportunity."),

    WarmupTopicAndPrompt(topic = "Data-Driven Marketing for Retail", prompt = "Draft an email for retail marketers. Subject: 'Boost Sales: Data-Driven Marketing Strategies'. The body should cover the power of data in customer targeting, examples of successful campaigns, and a call-to-action for a strategy session."),

    WarmupTopicAndPrompt(topic = "Blockchain in Supply Chain Management", prompt = "Generate an email for logistics managers. Subject: 'Trust and Transparency: Blockchain in Your Supply Chain'. The body should explain the benefits of blockchain in tracking goods, case studies, and offer a blockchain consultation."),

    WarmupTopicAndPrompt(topic = "Robotics in Healthcare", prompt = "Create an email aimed at healthcare providers. Subject: 'Elevate Patient Care: Robotics in Healthcare'. The body should discuss robotic-assisted surgery, rehabilitation, and patient monitoring, with case studies and an offer for a demo."),

    WarmupTopicAndPrompt(topic = "5G for Enterprises", prompt = "Write an email for business executives. Subject: 'Unlock Potential: How 5G Can Transform Your Business'. The body should explain the benefits of 5G in business operations, speed improvements, and invite for a consultation."),

    WarmupTopicAndPrompt(topic = "Voice Search Optimization", prompt = "Draft an email for digital marketers. Subject: 'Rank Higher: Voice Search Optimization for 2025'. The body should cover the rise of voice search, tips for optimization, and offer a personalized SEO audit."),

    WarmupTopicAndPrompt(topic = "Cybersecurity for Remote Workforces", prompt = "Generate an email for HR or IT leaders. Subject: 'Secure Your Remote Workforce: Cybersecurity Strategies for 2025'. The body should outline risks and solutions, and offer a cybersecurity audit for remote work policies."),

    WarmupTopicAndPrompt(topic = "AI Chatbots in Customer Support", prompt = "Write an email targeted to customer service managers. Subject: 'Enhance Support: AI Chatbots for 24/7 Customer Service'. The body should explain the benefits of AI chatbots, share success stories, and offer a chatbot integration demo."),

    WarmupTopicAndPrompt(topic = "Tech-Enabled Fleet Management", prompt = "Create an email for logistics or fleet managers. Subject: 'Maximize Efficiency: Tech-Enabled Fleet Management Solutions'. The body should discuss GPS tracking, real-time data, cost savings, and propose a fleet management consultation."),

    WarmupTopicAndPrompt(topic = "Cloud Security Trends", prompt = "Draft an email for IT security officers. Subject: 'Stay Protected: Cloud Security Trends in 2025'. The body should cover emerging threats, compliance requirements, and offer a security audit for cloud services."),

    WarmupTopicAndPrompt(topic = "Data Privacy in Marketing", prompt = "Write an email for marketing managers. Subject: 'Ensure Trust: Data Privacy Best Practices in Marketing'. The body should cover data protection laws, trust-building strategies, and offer a privacy compliance audit."),

    WarmupTopicAndPrompt(topic = "Digital Twins in Manufacturing", prompt = "Generate an email for manufacturing managers. Subject: 'Innovate Production: Harness the Power of Digital Twins'. The body should explain how digital twins improve efficiency, predictive maintenance, and offer a demo session."),

    WarmupTopicAndPrompt(topic = "Artificial Intelligence in Finance", prompt = "Create an email targeted to finance executives. Subject: 'Unlock Insights: AI Solutions for Finance'. The body should explain how AI can improve risk analysis, fraud detection, and portfolio optimization, with examples of use cases."),

    WarmupTopicAndPrompt(topic = "Automation in Customer Service", prompt = "Draft an email for customer service leaders. Subject: 'Automate Success: AI-Powered Customer Service'. The body should explain automation benefits, reduce costs, and invite to a free consultation."),

    WarmupTopicAndPrompt(topic = "Digital Payment Solutions", prompt = "Write an email for payment solution providers. Subject: 'Upgrade Your Business: Next-Gen Digital Payment Solutions'. The body should cover the benefits of digital payments, security, and ease of integration."),

    WarmupTopicAndPrompt(topic = "AI-Powered Predictive Analytics", prompt = "Generate an email for data scientists. Subject: 'Predict the Future: Harness AI for Predictive Analytics'. The body should explain how predictive analytics helps businesses, real-life case studies, and offer a workshop."),

    WarmupTopicAndPrompt(topic = "Sustainable Packaging Solutions", prompt = "Create an email for packaging companies. Subject: 'Go Green: Sustainable Packaging Solutions for 2025'. The body should discuss the environmental impact, cost-saving benefits, and offer a packaging audit."),

    WarmupTopicAndPrompt(topic = "Edge Computing in Healthcare", prompt = "Write an email for healthcare professionals. Subject: 'Transform Patient Care: Edge Computing in Healthcare'. The body should explain how edge computing reduces latency, enhances data processing, and propose a solution session."),

    WarmupTopicAndPrompt(topic = "Blockchain for Healthcare", prompt = "Draft an email targeted to healthcare administrators. Subject: 'Secure Your Data: Blockchain in Healthcare'. The body should explain how blockchain can ensure data privacy, traceability, and improve care coordination."),

    WarmupTopicAndPrompt(topic = "IoT in Smart Agriculture", prompt = "Generate an email for agritech companies. Subject: 'Revolutionize Farming: IoT Solutions for Smart Agriculture'. The body should explain the benefits of IoT in monitoring crop health, soil moisture, and offer a product demo."),

    WarmupTopicAndPrompt(topic = "AI for Financial Forecasting", prompt = "Write an email for financial analysts. Subject: 'Future-Proof Finance: AI for Predictive Forecasting'. The body should discuss AI-based financial forecasting tools, use cases, and propose a consultation."),

    WarmupTopicAndPrompt(topic = "Intelligent Automation in IT Operations", prompt = "Create an email for IT operations teams. Subject: 'Simplify IT Operations: Intelligent Automation for 2025'. The body should explain automation benefits, tools to streamline tasks, and invite to a demo."),

    WarmupTopicAndPrompt(topic = "AI in Education", prompt = "Draft an email for educational institutions. Subject: 'Enhance Learning: The Role of AI in Education'. The body should explain AI-driven learning platforms, personalization benefits, and suggest a pilot program."),

    WarmupTopicAndPrompt(topic = "SaaS for Small Businesses", prompt = "Write an email for small business owners. Subject: 'Transform Your Business: How SaaS Solutions Can Help'. The body should outline the benefits of SaaS for cost savings, scalability, and efficiency, with an offer for a free consultation."),

    WarmupTopicAndPrompt(topic = "Smart City Technologies", prompt = "Generate an email for urban planners. Subject: 'Building Tomorrow: Embrace Smart City Solutions'. The body should discuss the integration of IoT, smart infrastructure, and sustainability benefits."),

    WarmupTopicAndPrompt(topic = "AI for Fraud Detection", prompt = "Create an email for financial institutions. Subject: 'Stay Safe: AI Solutions for Fraud Detection'. The body should cover AI's role in fraud prevention, success stories, and offer a consultation on AI implementation."),

    WarmupTopicAndPrompt(topic = "Custom Software Development", prompt = "Write an email for businesses in need of custom software. Subject: 'Scale with Ease: Custom Software Development Solutions'. The body should explain the benefits of tailored software, case studies, and offer a free consultation."),

    WarmupTopicAndPrompt(topic = "Sustainable Urban Development", prompt = "Draft an email for city developers. Subject: 'Build Smart: Sustainable Urban Development Solutions'. The body should cover eco-friendly urban planning practices, green building certifications, and invite to a strategy session."),

    WarmupTopicAndPrompt(topic = "AI-Powered Customer Insights", prompt = "Generate an email for marketing teams. Subject: 'Know Your Customers: AI for Actionable Insights'. The body should explain how AI can extract insights from customer data, improving marketing strategies."),

    WarmupTopicAndPrompt(topic = "Cloud-Based Project Management Tools", prompt = "Write an email for project managers. Subject: 'Boost Collaboration: Cloud-Based Project Management Tools'. The body should cover the advantages of cloud tools for remote teams, real-time updates, and efficiency gains."),

    WarmupTopicAndPrompt(topic = "Smart Manufacturing Technologies", prompt = "Create an email for manufacturers. Subject: 'Revolutionize Production: Embrace Smart Manufacturing Technologies'. The body should explain IoT, robotics, and data analytics in manufacturing, with a case study and demo offer."),

  )

  def getRandomWarmupTopicAndPrompt: WarmupTopicAndPrompt = {

    warmupTopicsAndPrompts(scala.util.Random.nextInt(warmupTopicsAndPrompts.size))

  }

}
