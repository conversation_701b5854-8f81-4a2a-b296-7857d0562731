package utils
import java.util.concurrent.Executors
import scalikejdbc.config.DBs
import akka.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import play.api.{Logger, Logging}

import scala.concurrent.ExecutionContext
import play.api.libs.ws.ahc.AhcWSClient
import utils.cronjobs._
import utils.email.EmailSendingService
import utils.mq.{MQEmailScheduler, MQEmailSchedulerForLandingCheck, MQEmailSchedulerForReply, MQEmailSchedulerV2, MQResetSRCredits, MQEmailHealthCheck}
import utils.mq.email.{MQEmail, MQEmailMessage}

import scala.util.{Failure, Success, Try}

object App extends Logging{
  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      implicit val system: ActorSystem = ActorSystem()
      implicit val wSClient: AhcWSClient = AhcWSClient()
      implicit val actorContext: ExecutionContext = system.dispatcher

      val applicationName = args(0)
      applicationName match {

        case "worker" =>

          val emailSchedulerEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val emailSchedulerV2EC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val emailSchedulerForReplyEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val emailSchedulerForLandingCheckEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val mqEmailEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val mqResetSRCreditsEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(100))
          val mqEmailHealthCheckEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(30))

          MQEmailScheduler.startConsumer()(ws = wSClient, system = system, ec = emailSchedulerEC)
            .flatMap { _ => MQEmailSchedulerV2.startConsumer()(ws = wSClient, system = system, ec = emailSchedulerV2EC) }
            .flatMap { _ => MQEmailSchedulerForReply.startConsumer()(ws = wSClient, system = system, ec = emailSchedulerForReplyEC) }
            .flatMap { _ => MQEmailSchedulerForLandingCheck.startConsumer()(ws = wSClient, system = system, ec = emailSchedulerForLandingCheckEC)}
            .flatMap { _ => MQEmail.startConsumer()(ws = wSClient, system = system, ec = mqEmailEC)}
            .flatMap { _ => MQResetSRCredits.startConsumer()(ws = wSClient, system = system, ec = mqResetSRCreditsEC)}
            .flatMap { _ => MQEmailHealthCheck.startConsumer()(ws = wSClient, system = system, ec = mqEmailHealthCheckEC)}

          match {

            case Failure(e) =>
              logger.error("[email_scheduler_worker] error while starting: " + Helpers.getStackTraceAsString(e))
              wSClient.close()

            case Success(startedStr) => logger.info("[email_scheduler_worker] started successfully: " + startedStr)
          }


        case "scheduler" =>

          implicit val wSClient = AhcWSClient()

          val scheduleSendingCronMqEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
          val scheduleReplyCronMqEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
          val scheduleProcessSendCronMqEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
          val scheduleLandingCronMqEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
          val oneMinutelyCronEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          EmailSendingCronService.start()(system = system, ec = scheduleSendingCronMqEC, wSClient)
          EmailReplyIngCronService.start()(system = system, ec = scheduleReplyCronMqEC, wSClient)
          EmailProcessSendCronService.start()(system = system, ec = scheduleProcessSendCronMqEC, wSClient)
          EmailLandingCronService.start()(system = system, ec = scheduleLandingCronMqEC, wSClient)
          OneMinutelyCronService.start()(system = system, ec = oneMinutelyCronEC, wSClient)


        case "demo" =>


          implicit val Logger = new SRLogger(logRequestId = "demo")

          //reset credist
//            AccountDB.resetSRCreditsDailyFromCron(accountId = 1, Logger = Logger)

          //schedule
//          EmailSendingCronService.scheduleEmailAccountForSend(emailAccountId = 32, Logger = Logger)

          //send
          val msg = MQEmailMessage(emailScheduledId = 262, logRequestId = Some("demo"))
          EmailSendingService.processSendEmailRequest(msg)

          //check landing
//          EmailLandingService.checkLanding(emailScheduledId = 1059, Logger = Logger)

//          EmailAccount.findForScheduling(2)

          //reply
//            EmailReplyIngCronService.scheduleEmailAccountForReply(emailScheduledId = 260, Logger = Logger)

//          val d = EmailAccount.findReceiverEmailAccountForScheduling(1, 25).get;
//          Logger.info(s"dfgegrg, $d")

//          EmailScheduled.updateLandingFolder(
//            emailScheduledId = 198,
//            originalLandedFolder = "spam",
//            landedFolder = "inbox",
//            landedFolderType = FolderType.CATEGORY,
//            Logger = Logger
//          ) match {
//
//            case Failure(e) =>
//
//              Logger.info(s"\n\n\n Failure ${e.getMessage}")
//
//            case Success(value) =>
//              Logger.info(s"\n\n\n Success ${value}")
//
//          }



        case _ => logger.error(s"Invalid application name: $applicationName")
      }
    } else {
      logger.error("Application name not provided")
    }
  }
}
