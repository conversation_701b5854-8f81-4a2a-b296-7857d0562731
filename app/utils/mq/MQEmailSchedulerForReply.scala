package utils.mq

import akka.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.cronjobs.{EmailReplyIngCronService}
import utils.{Help<PERSON>, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MQEmailSchedulerMessageForReply(emailScheduledId: Long)

object MQEmailSchedulerForReply extends MQService[MQEmailSchedulerMessageForReply] {


  implicit val system: ActorSystem = ActorSystem()

  val queueBaseName: String = MQConfig.emailAccountSchedulerForReplyQueueBaseName
  val prefetchCount: Int = MQConfig.emailSchedulerForReplyPrefetchCount

  def publish(msg: MQEmailSchedulerMessageForReply) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQEmailSchedulerMessageForReply)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQEmailSchedulerForReply] scheduleEmailAccountForReply ea_${msg.emailScheduledId} :: "

    val Logger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    EmailReplyIngCronService.scheduleEmailAccountForReply(emailScheduledId = msg.emailScheduledId, Logger = Logger)
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.info(s"scheduled:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

        res._1

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.error(s"failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: ${Helpers.getStackTraceAsString(e)}")

        0
      }


  }

}