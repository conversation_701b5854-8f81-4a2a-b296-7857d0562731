package utils.mq

import akka.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.models.EmailScheduled
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.cronjobs.{EmailLandingCronService, EmailReplyIngCronService}
import utils.email.EmailLandingService
import utils.{Helpers, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class MQEmailScheduledForLandingCheck(emailScheduledId: Long)

object MQEmailSchedulerForLandingCheck extends MQService[MQEmailScheduledForLandingCheck] {


  implicit val system: ActorSystem = ActorSystem()

  val queueBaseName: String = MQConfig.emailAccountSchedulerForLandingCheckQueueBaseName
  val prefetchCount: Int = MQConfig.emailSchedulerForForLandingCheckPrefetchCount

  def publish(msg: MQEmailScheduledForLandingCheck) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQEmailScheduledForLandingCheck)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQEmailScheduledForLandingCheck] checkLanding es_${msg.emailScheduledId} :: "

    val Logger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    EmailLandingService.checkLanding(emailScheduledId = msg.emailScheduledId, Logger = Logger)
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.info(s"scheduled:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")


        Try {

          EmailScheduled.updateIsScheduledForLandingCheck(emailScheduledId = msg.emailScheduledId, Logger = Logger)

        } match {

          case Failure(e) =>

            Logger.fatal(s"${Helpers.getStackTraceAsString(e)}")
            false

          case Success(_) =>
            Logger.info(s"DONE")

            res._2

        }

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.error(s"failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: ${Helpers.getStackTraceAsString(e)}")

        false
      }


  }

}