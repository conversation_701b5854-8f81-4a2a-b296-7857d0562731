package utils.cronjobs

import akka.actor.ActorSystem
import play.api.Logger
import play.api.libs.ws.ahc.AhcWSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._
import scala.util.{Failure, Success, Try}


trait CronTrait {
  val cronName: String
  val cronIntervalInSeconds: Int

  val logger = new SRLogger(
    logRequestId = s"[$cronName]"
  )

  def executeCron(
                   logger: SRLogger
                 )(
                   implicit system: ActorSystem,
                   ec: ExecutionContext,
                   wSClient: AhcWSClient
                 ): Unit


  final def start()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) = {

    logger.info(s"[$cronName] start schedule")

    val interval = cronIntervalInSeconds * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(10000.milliseconds, interval)(() => execute())

  }

  final def execute()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) = {


    logger.info("start execute")

    // NOTE: https://stackoverflow.com/questions/32255919/akka-scheduler-stops-on-exception-is-it-expected
    Try {

      executeCron(
        logger = logger
      )

    } match {

      case Failure(e) => logger.fatal(s"execute error", err = e)

      case Success(_) => logger.info(s"execute success")

    }
  }
}
