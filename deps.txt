[info] welcome to sbt 1.9.8 (Amazon.com Inc. Java 21.0.2)
[info] loading global plugins from /Users/<USER>/.sbt/1.0/plugins
[info] loading settings for project emailwarmup_backend-build from assembly.sbt,plugins.sbt ...
[info] loading project definition from /Users/<USER>/Dev/emailwarmup_backend/project
[info] loading settings for project srwarmup from build.sbt ...
[info]   __              __
[info]   \ \     ____   / /____ _ __  __
[info]    \ \   / __ \ / // __ `// / / /
[info]    / /  / /_/ // // /_/ // /_/ /
[info]   /_/  / .___//_/ \__,_/ \__, /
[info]       /_/               /____/
[info] 
[info] Version 2.9.1 running Java 21.0.2
[info] 
[info] Play is run entirely by the community. Please consider contributing and/or donating:
[info] https://www.playframework.com/sponsors
[info] 
[info] Installing gs:// and artifactregistry:// URLStreamHandlers for ProjectRef(file:/Users/<USER>/Dev/emailwarmup_backend/,srwarmup)
[info] srwarmup:srwarmup_2.13:1.0 [S]
[info]   +-ai.x:play-json-extensions_2.13:0.40.2 [S]
[info]   | +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | 
[info]   | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | | 
[info]   | +-com.typesafe.play:play-json_2.13:2.7.4 (evicted by: 2.10.3)
[info]   | 
[info]   +-ch.qos.logback:logback-classic:1.5.6
[info]   | +-org.slf4j:slf4j-api:2.0.13 (evicted by: 2.0.16)
[info]   | 
[info]   +-com.github.karelcemus:play-redis_2.13:2.7.0 [S]
[info]   | +-com.github.karelcemus:rediscala_2.13:1.9.1 [S]
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.5.23 (evicted by: 2.6.21)
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-org.scala-stm:scala-stm_2.13:0.9.1 [S]
[info]   |   
[info]   +-com.google.cloud:google-cloud-logging-logback:0.123.4-alpha
[info]   | +-ch.qos.logback:logback-classic:1.2.10 (evicted by: 1.5.6)
[info]   | +-ch.qos.logback:logback-classic:1.5.6
[info]   | | +-org.slf4j:slf4j-api:2.0.13 (evicted by: 2.0.16)
[info]   | | 
[info]   | +-ch.qos.logback:logback-core:1.2.10 (evicted by: 1.5.6)
[info]   | +-ch.qos.logback:logback-core:1.5.6
[info]   | +-com.google.android:annotations:4.1.1.4
[info]   | +-com.google.api.grpc:proto-google-cloud-logging-v2:0.95.3 (evicted by: 0...
[info]   | +-com.google.api.grpc:proto-google-cloud-logging-v2:0.95.4
[info]   | | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2.21..
[info]   | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | +-com.google.guava:failureaccess:1.0.1
[info]   | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-..
[info]   | | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | 
[info]   | +-com.google.api.grpc:proto-google-common-protos:2.7.2
[info]   | +-com.google.api.grpc:proto-google-iam-v1:1.2.1
[info]   | +-com.google.api:api-common:2.1.3
[info]   | +-com.google.api:gax-grpc:2.11.0
[info]   | +-com.google.api:gax:2.11.0
[info]   | +-com.google.auth:google-auth-library-credentials:1.4.0
[info]   | +-com.google.auth:google-auth-library-oauth2-http:1.4.0
[info]   | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | +-com.google.cloud:google-cloud-core-grpc:2.4.0
[info]   | +-com.google.cloud:google-cloud-core:2.4.0
[info]   | +-com.google.cloud:google-cloud-logging:3.6.3 (evicted by: 3.6.4)
[info]   | +-com.google.cloud:google-cloud-logging:3.6.4
[info]   | | +-com.google.android:annotations:4.1.1.4
[info]   | | +-com.google.api.grpc:proto-google-cloud-logging-v2:0.95.4
[info]   | | | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2...
[info]   | | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | | +-com.google.guava:failureaccess:1.0.1
[info]   | | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   | | | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | | | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | | 
[info]   | | +-com.google.api.grpc:proto-google-common-protos:2.7.2
[info]   | | +-com.google.api.grpc:proto-google-iam-v1:1.2.1
[info]   | | +-com.google.api:api-common:2.1.3
[info]   | | +-com.google.api:gax-grpc:2.11.0
[info]   | | +-com.google.api:gax:2.11.0
[info]   | | +-com.google.auth:google-auth-library-credentials:1.4.0
[info]   | | +-com.google.auth:google-auth-library-oauth2-http:1.4.0
[info]   | | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | | +-com.google.cloud:google-cloud-core-grpc:2.4.0
[info]   | | +-com.google.cloud:google-cloud-core:2.4.0
[info]   | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | +-com.google.code.gson:gson:2.9.0
[info]   | | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2.21..
[info]   | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | +-com.google.guava:failureaccess:1.0.1
[info]   | | +-com.google.guava:guava:31.0.1-jre (evicted by: 32.1.3-jre)
[info]   | | +-com.google.guava:guava:32.1.3-jre
[info]   | | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | | +-com.google.guava:failureaccess:1.0.1
[info]   | | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   | | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | | 
[info]   | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-..
[info]   | | +-com.google.http-client:google-http-client-gson:1.41.2
[info]   | | +-com.google.http-client:google-http-client:1.41.2
[info]   | | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | +-com.google.protobuf:protobuf-java-util:3.19.3
[info]   | | +-com.google.protobuf:protobuf-java:3.19.3
[info]   | | +-com.google.re2j:re2j:1.5
[info]   | | +-commons-codec:commons-codec:1.15
[info]   | | +-io.grpc:grpc-alts:1.44.0
[info]   | | +-io.grpc:grpc-api:1.44.0
[info]   | | +-io.grpc:grpc-auth:1.44.0
[info]   | | +-io.grpc:grpc-context:1.44.0
[info]   | | +-io.grpc:grpc-core:1.44.0
[info]   | | +-io.grpc:grpc-grpclb:1.44.0
[info]   | | +-io.grpc:grpc-netty-shaded:1.44.0
[info]   | | +-io.grpc:grpc-protobuf-lite:1.44.0
[info]   | | +-io.grpc:grpc-protobuf:1.44.0
[info]   | | +-io.grpc:grpc-services:1.44.0
[info]   | | +-io.grpc:grpc-stub:1.44.0
[info]   | | +-io.grpc:grpc-xds:1.44.0
[info]   | | +-io.opencensus:opencensus-api:0.31.0
[info]   | | +-io.opencensus:opencensus-contrib-http-util:0.31.0
[info]   | | +-io.opencensus:opencensus-proto:0.2.0
[info]   | | +-io.perfmark:perfmark-api:0.23.0
[info]   | | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | | +-org.apache.httpcomponents:httpclient:4.5.13
[info]   | | | +-commons-codec:commons-codec:1.11 (evicted by: 1.15)
[info]   | | | +-commons-codec:commons-codec:1.15
[info]   | | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   | | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | | 
[info]   | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | +-org.bouncycastle:bcpkix-jdk15on:1.67
[info]   | | +-org.bouncycastle:bcprov-jdk15on:1.67
[info]   | | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | +-org.codehaus.mojo:animal-sniffer-annotations:1.20
[info]   | | +-org.conscrypt:conscrypt-openjdk-uber:2.5.1
[info]   | | +-org.threeten:threetenbp:1.5.2
[info]   | | 
[info]   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | +-com.google.code.gson:gson:2.8.9 (evicted by: 2.9.0)
[info]   | +-com.google.code.gson:gson:2.9.0
[info]   | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2.21.1)
[info]   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | +-com.google.guava:failureaccess:1.0.1
[info]   | +-com.google.guava:guava:31.0.1-jre (evicted by: 32.1.3-jre)
[info]   | +-com.google.guava:guava:32.1.3-jre
[info]   | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | +-com.google.guava:failureaccess:1.0.1
[info]   | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-..
[info]   | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | 
[info]   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-gu..
[info]   | +-com.google.http-client:google-http-client-gson:1.41.2
[info]   | +-com.google.http-client:google-http-client:1.41.2
[info]   | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | +-com.google.protobuf:protobuf-java-util:3.19.3
[info]   | +-com.google.protobuf:protobuf-java:3.19.3
[info]   | +-com.google.re2j:re2j:1.5
[info]   | +-commons-codec:commons-codec:1.15
[info]   | +-io.grpc:grpc-alts:1.44.0
[info]   | +-io.grpc:grpc-api:1.44.0
[info]   | +-io.grpc:grpc-auth:1.44.0
[info]   | +-io.grpc:grpc-context:1.44.0
[info]   | +-io.grpc:grpc-core:1.44.0
[info]   | +-io.grpc:grpc-grpclb:1.44.0
[info]   | +-io.grpc:grpc-netty-shaded:1.44.0
[info]   | +-io.grpc:grpc-protobuf-lite:1.44.0
[info]   | +-io.grpc:grpc-protobuf:1.44.0
[info]   | +-io.grpc:grpc-services:1.44.0
[info]   | +-io.grpc:grpc-stub:1.44.0
[info]   | +-io.grpc:grpc-xds:1.44.0
[info]   | +-io.opencensus:opencensus-api:0.31.0
[info]   | +-io.opencensus:opencensus-contrib-http-util:0.31.0
[info]   | +-io.opencensus:opencensus-proto:0.2.0
[info]   | +-io.perfmark:perfmark-api:0.23.0
[info]   | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | +-org.apache.httpcomponents:httpclient:4.5.13
[info]   | | +-commons-codec:commons-codec:1.11 (evicted by: 1.15)
[info]   | | +-commons-codec:commons-codec:1.15
[info]   | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | 
[info]   | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | +-org.bouncycastle:bcpkix-jdk15on:1.67
[info]   | +-org.bouncycastle:bcprov-jdk15on:1.67
[info]   | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | +-org.checkerframework:checker-qual:3.37.0
[info]   | +-org.codehaus.mojo:animal-sniffer-annotations:1.20
[info]   | +-org.conscrypt:conscrypt-openjdk-uber:2.5.1
[info]   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | +-org.slf4j:slf4j-api:2.0.16
[info]   | +-org.threeten:threetenbp:1.5.2
[info]   | 
[info]   +-com.google.cloud:google-cloud-logging:3.6.4
[info]   | +-com.google.android:annotations:4.1.1.4
[info]   | +-com.google.api.grpc:proto-google-cloud-logging-v2:0.95.4
[info]   | | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2.21..
[info]   | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | +-com.google.guava:failureaccess:1.0.1
[info]   | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-..
[info]   | | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | 
[info]   | +-com.google.api.grpc:proto-google-common-protos:2.7.2
[info]   | +-com.google.api.grpc:proto-google-iam-v1:1.2.1
[info]   | +-com.google.api:api-common:2.1.3
[info]   | +-com.google.api:gax-grpc:2.11.0
[info]   | +-com.google.api:gax:2.11.0
[info]   | +-com.google.auth:google-auth-library-credentials:1.4.0
[info]   | +-com.google.auth:google-auth-library-oauth2-http:1.4.0
[info]   | +-com.google.auto.value:auto-value-annotations:1.9
[info]   | +-com.google.cloud:google-cloud-core-grpc:2.4.0
[info]   | +-com.google.cloud:google-cloud-core:2.4.0
[info]   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | +-com.google.code.gson:gson:2.9.0
[info]   | +-com.google.errorprone:error_prone_annotations:2.11.0 (evicted by: 2.21.1)
[info]   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | +-com.google.guava:failureaccess:1.0.1
[info]   | +-com.google.guava:guava:31.0.1-jre (evicted by: 32.1.3-jre)
[info]   | +-com.google.guava:guava:32.1.3-jre
[info]   | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | +-com.google.guava:failureaccess:1.0.1
[info]   | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-..
[info]   | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | 
[info]   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-gu..
[info]   | +-com.google.http-client:google-http-client-gson:1.41.2
[info]   | +-com.google.http-client:google-http-client:1.41.2
[info]   | +-com.google.j2objc:j2objc-annotations:1.3 (evicted by: 2.8)
[info]   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | +-com.google.protobuf:protobuf-java-util:3.19.3
[info]   | +-com.google.protobuf:protobuf-java:3.19.3
[info]   | +-com.google.re2j:re2j:1.5
[info]   | +-commons-codec:commons-codec:1.15
[info]   | +-io.grpc:grpc-alts:1.44.0
[info]   | +-io.grpc:grpc-api:1.44.0
[info]   | +-io.grpc:grpc-auth:1.44.0
[info]   | +-io.grpc:grpc-context:1.44.0
[info]   | +-io.grpc:grpc-core:1.44.0
[info]   | +-io.grpc:grpc-grpclb:1.44.0
[info]   | +-io.grpc:grpc-netty-shaded:1.44.0
[info]   | +-io.grpc:grpc-protobuf-lite:1.44.0
[info]   | +-io.grpc:grpc-protobuf:1.44.0
[info]   | +-io.grpc:grpc-services:1.44.0
[info]   | +-io.grpc:grpc-stub:1.44.0
[info]   | +-io.grpc:grpc-xds:1.44.0
[info]   | +-io.opencensus:opencensus-api:0.31.0
[info]   | +-io.opencensus:opencensus-contrib-http-util:0.31.0
[info]   | +-io.opencensus:opencensus-proto:0.2.0
[info]   | +-io.perfmark:perfmark-api:0.23.0
[info]   | +-javax.annotation:javax.annotation-api:1.3.2
[info]   | +-org.apache.httpcomponents:httpclient:4.5.13
[info]   | | +-commons-codec:commons-codec:1.11 (evicted by: 1.15)
[info]   | | +-commons-codec:commons-codec:1.15
[info]   | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | 
[info]   | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | +-org.bouncycastle:bcpkix-jdk15on:1.67
[info]   | +-org.bouncycastle:bcprov-jdk15on:1.67
[info]   | +-org.checkerframework:checker-qual:3.21.1 (evicted by: 3.37.0)
[info]   | +-org.checkerframework:checker-qual:3.37.0
[info]   | +-org.codehaus.mojo:animal-sniffer-annotations:1.20
[info]   | +-org.conscrypt:conscrypt-openjdk-uber:2.5.1
[info]   | +-org.threeten:threetenbp:1.5.2
[info]   | 
[info]   +-com.iheart:ficus_2.13:1.4.7 [S]
[info]   | +-com.typesafe:config:1.3.4 (evicted by: 1.4.3)
[info]   | +-com.typesafe:config:1.4.3
[info]   | 
[info]   +-com.rabbitmq:amqp-client:4.0.0
[info]   | +-org.slf4j:slf4j-api:1.7.21 (evicted by: 2.0.16)
[info]   | +-org.slf4j:slf4j-api:2.0.16
[info]   | 
[info]   +-com.smartreach:sr_email_service_providers_2.13:1.0.1 [S]
[info]   | +-ai.x:play-json-extensions_2.13:0.40.2 [S]
[info]   | | +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | 
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | | | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | | | 
[info]   | | +-com.typesafe.play:play-json_2.13:2.7.4 (evicted by: 2.10.3)
[info]   | | 
[info]   | +-com.smartreach:sr_logger_2.13:1.0.2 (evicted by: 3.2.3)
[info]   | +-com.smartreach:sr_logger_2.13:3.2.3
[info]   | +-com.sun.mail:jakarta.mail:1.6.7
[info]   | | +-com.sun.activation:jakarta.activation:1.2.1
[info]   | | 
[info]   | +-com.typesafe.play:play-ws_2.13:2.9.1 [S]
[info]   | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | 
[info]   | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | 
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | 
[info]   | | +-com.typesafe.play:play-ws-standalone-json_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | 
[info]   | | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | |   
[info]   | | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | |   
[info]   | | | | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | | | | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | | | | 
[info]   | | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | |   +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | |   +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | | +-com.typesafe:config:1.4.3
[info]   | | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | |   | | 
[info]   | | |   | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | |   | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | |   | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | | +-com.typesafe:config:1.4.3
[info]   | | |   | | 
[info]   | | |   | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   | 
[info]   | | |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | +-com.typesafe:config:1.4.3
[info]   | | |   | 
[info]   | | |   +-javax.inject:javax.inject:1
[info]   | | |   
[info]   | | +-com.typesafe.play:play-ws-standalone-xml_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | | | +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | | 
[info]   | | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | | 
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | 
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-javax.inject:javax.inject:1
[info]   | | | | 
[info]   | | | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | | | 
[info]   | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | 
[info]   | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | 
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | 
[info]   | | | +-javax.inject:javax.inject:1
[info]   | | | 
[info]   | | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | 
[info]   | |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | 
[info]   | |   +-com.google.guava:guava:32.1.3-jre
[info]   | |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | |   | +-com.google.guava:failureaccess:1.0.1
[info]   | |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-w..
[info]   | |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | |   | +-org.checkerframework:checker-qual:3.37.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by:..
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2...
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (e..
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evict..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | | 
[info]   | |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.lz4:lz4-java:1.8.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-build-link:2.9.1
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | 
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | | 
[info]   | |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | 
[info]   | |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   | |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-javax.inject:javax.inject:1
[info]   | |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   
[info]   | +-joda-time:joda-time:2.12.2 (evicted by: 2.12.7)
[info]   | +-joda-time:joda-time:2.12.7
[info]   | +-org.apache.commons:commons-text:1.8
[info]   | | +-org.apache.commons:commons-lang3:3.9
[info]   | | 
[info]   | +-org.jsoup:jsoup:1.15.4
[info]   | 
[info]   +-com.smartreach:sr_llm_service_2.13:3.2.3 [S]
[info]   | +-com.smartreach:sr_logger_2.13:3.2.3
[info]   | +-joda-time:joda-time:2.12.7
[info]   | +-software.amazon.awssdk:bedrock:2.26.22
[info]   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | +-software.amazon.awssdk:apache-client:2.26.22
[info]   | | | +-commons-codec:commons-codec:1.15
[info]   | | | +-org.apache.httpcomponents:httpclient:4.5.13
[info]   | | | | +-commons-codec:commons-codec:1.11 (evicted by: 1.15)
[info]   | | | | +-commons-codec:commons-codec:1.15
[info]   | | | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   | | | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | | | 
[info]   | | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   | | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:auth:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | 
[info]   | | +-software.amazon.awssdk:aws-core:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:auth:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | 
[info]   | | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | 
[info]   | | +-software.amazon.awssdk:aws-json-protocol:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:aws-core:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:auth:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | 
[info]   | | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | 
[info]   | | | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | 
[info]   | | | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | |   
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | 
[info]   | | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | 
[info]   | | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | |   
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:protocol-core:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | 
[info]   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | |   
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | 
[info]   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:netty-nio-client:2.26.22
[info]   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | 
[info]   | | | +-io.netty:netty-codec-http2:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-codec-http:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-handler:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | | 
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |   | 
[info]   | | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |     
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | | 
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |   | 
[info]   | | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | |     
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-handler:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   | 
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | |     
[info]   | | | +-io.netty:netty-codec-http:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-handler:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | | 
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   | 
[info]   | | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | |     
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   | 
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | |     
[info]   | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   | 
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | |     
[info]   | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | +-io.netty:netty-handler:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   | 
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | |     
[info]   | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | 
[info]   | | | +-io.netty:netty-transport-classes-epoll:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | | 
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   | 
[info]   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | | |     
[info]   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   | 
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   | | | |     
[info]   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | | 
[info]   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   | | | |   
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:protocol-core:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:regions:2.26.22
[info]   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | 
[info]   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | |   
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | 
[info]   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | 
[info]   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | |   
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | |   
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | | |   
[info]   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | | |   
[info]   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | | |   
[info]   | | +-software.amazon.awssdk:utils:2.26.22
[info]   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   | |   
[info]   | +-software.amazon.awssdk:bedrockruntime:2.25.52
[info]   |   +-software.amazon.awssdk:annotations:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   +-software.amazon.awssdk:apache-client:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:apache-client:2.26.22
[info]   |   | +-commons-codec:commons-codec:1.15
[info]   |   | +-org.apache.httpcomponents:httpclient:4.5.13
[info]   |   | | +-commons-codec:commons-codec:1.11 (evicted by: 1.15)
[info]   |   | | +-commons-codec:commons-codec:1.15
[info]   |   | | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   |   | | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   |   | | 
[info]   |   | +-org.apache.httpcomponents:httpcore:4.4.13 (evicted by: 4.4.15)
[info]   |   | +-org.apache.httpcomponents:httpcore:4.4.15
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:auth:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:auth:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:regions:2.26.22
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | 
[info]   |   +-software.amazon.awssdk:aws-core:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:aws-core:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:auth:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:regions:2.26.22
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | 
[info]   |   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:regions:2.26.22
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | 
[info]   |   +-software.amazon.awssdk:aws-json-protocol:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:aws-json-protocol:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:aws-core:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:auth:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws-eventstream:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:regions:2.26.22
[info]   |   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | 
[info]   |   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | 
[info]   |   | | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | 
[info]   |   | | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | |   
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | 
[info]   |   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:regions:2.26.22
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | 
[info]   |   | | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | |   
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.eventstream:eventstream:1.0.1
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:protocol-core:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | 
[info]   |   | | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | |   
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:endpoints-spi:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | 
[info]   |   +-software.amazon.awssdk:http-auth-aws:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:http-auth-spi:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:http-auth:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:http-auth:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:http-client-spi:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:identity-spi:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:json-utils:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:metrics-spi:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:netty-nio-client:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:netty-nio-client:2.26.22
[info]   |   | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | 
[info]   |   | +-io.netty:netty-codec-http2:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-codec-http:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-handler:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | | 
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |   | 
[info]   |   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |     
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | | 
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |   | 
[info]   |   | | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | |     
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-handler:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   | 
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | |     
[info]   |   | +-io.netty:netty-codec-http:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-handler:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | | 
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   | 
[info]   |   | | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | |     
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   | 
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | |     
[info]   |   | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   | 
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | |     
[info]   |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | +-io.netty:netty-handler:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-codec:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   | 
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | |     
[info]   |   | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | 
[info]   |   | +-io.netty:netty-transport-classes-epoll:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-transport-native-unix-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | | 
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   | 
[info]   |   | | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | | |     
[info]   |   | | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | |   +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | |   | +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   | 
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |     +-io.netty:netty-common:4.1.111.Final
[info]   |   | |     
[info]   |   | +-io.netty:netty-transport:4.1.111.Final
[info]   |   | | +-io.netty:netty-buffer:4.1.111.Final
[info]   |   | | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | | 
[info]   |   | | +-io.netty:netty-common:4.1.111.Final
[info]   |   | | +-io.netty:netty-resolver:4.1.111.Final
[info]   |   | |   +-io.netty:netty-common:4.1.111.Final
[info]   |   | |   
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:protocol-core:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:protocol-core:2.26.22
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:regions:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:regions:2.26.22
[info]   |   | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:json-utils:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:third-party-jackson-core:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | 
[info]   |   | | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | |   
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:sdk-core:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:sdk-core:2.26.22
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:endpoints-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | 
[info]   |   | +-software.amazon.awssdk:http-auth-aws:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | 
[info]   |   | | +-software.amazon.awssdk:checksums:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:checksums-spi:2.26.22
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | |   
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-auth-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | |   
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:http-client-spi:2.26.22
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:identity-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:metrics-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:profiles:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:retries:2.26.22
[info]   |   | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | +-software.amazon.awssdk:retries-spi:2.26.22
[info]   |   | | | +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | | |   
[info]   |   | | +-software.amazon.awssdk:utils:2.26.22
[info]   |   | |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   | |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   | |   
[info]   |   | +-software.amazon.awssdk:utils:2.26.22
[info]   |   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   |   +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   |   +-software.amazon.awssdk:annotations:2.26.22
[info]   |   |   
[info]   |   +-software.amazon.awssdk:utils:2.25.52 (evicted by: 2.26.22)
[info]   |   +-software.amazon.awssdk:utils:2.26.22
[info]   |     +-org.reactivestreams:reactive-streams:1.0.4
[info]   |     +-org.slf4j:slf4j-api:1.7.30 (evicted by: 2.0.16)
[info]   |     +-org.slf4j:slf4j-api:2.0.16
[info]   |     +-software.amazon.awssdk:annotations:2.26.22
[info]   |     
[info]   +-com.smartreach:sr_logger_2.13:3.2.3
[info]   +-com.sun.mail:gimap:1.6.7
[info]   | +-com.sun.mail:imap:1.6.7
[info]   | | +-com.sun.mail:mailapi:1.6.7
[info]   | |   +-com.sun.activation:jakarta.activation:1.2.1
[info]   | |   
[info]   | +-com.sun.mail:mailapi:1.6.7
[info]   |   +-com.sun.activation:jakarta.activation:1.2.1
[info]   |   
[info]   +-com.sun.mail:jakarta.mail:1.6.7
[info]   | +-com.sun.activation:jakarta.activation:1.2.1
[info]   | 
[info]   +-com.typesafe.play:play-ahc-ws_2.13:2.9.1 [S]
[info]   | +-com.typesafe.play:play-ahc-ws-standalone_2.13:2.2.5 [S]
[info]   | | +-com.typesafe.play:cachecontrol_2.13:2.3.1 [S]
[info]   | | | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | | | 
[info]   | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | 
[info]   | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | 
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | 
[info]   | | | +-javax.inject:javax.inject:1
[info]   | | | 
[info]   | | +-com.typesafe.play:shaded-asynchttpclient:2.2.5
[info]   | | +-com.typesafe.play:shaded-oauth:2.2.5
[info]   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | | 
[info]   | +-com.typesafe.play:play-ws_2.13:2.9.1 [S]
[info]   | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | 
[info]   | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | 
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | 
[info]   | | +-com.typesafe.play:play-ws-standalone-json_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | 
[info]   | | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | |   
[info]   | | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | |   
[info]   | | | | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | | | | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | | | | 
[info]   | | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | |   +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | |   +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | | +-com.typesafe:config:1.4.3
[info]   | | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | |   | | 
[info]   | | |   | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | |   | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | |   | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | | +-com.typesafe:config:1.4.3
[info]   | | |   | | 
[info]   | | |   | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | |   | 
[info]   | | |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | |   | +-com.typesafe:config:1.4.3
[info]   | | |   | 
[info]   | | |   +-javax.inject:javax.inject:1
[info]   | | |   
[info]   | | +-com.typesafe.play:play-ws-standalone-xml_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | | | +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | | 
[info]   | | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | | 
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | | 
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-javax.inject:javax.inject:1
[info]   | | | | 
[info]   | | | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | | | 
[info]   | | +-com.typesafe.play:play-ws-standalone_2.13:2.2.5 [S]
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.20 (evicted by: 2.6.21)
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | 
[info]   | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | 
[info]   | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | 
[info]   | | | +-javax.inject:javax.inject:1
[info]   | | | 
[info]   | | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | 
[info]   | |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | 
[info]   | |   +-com.google.guava:guava:32.1.3-jre
[info]   | |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | |   | +-com.google.guava:failureaccess:1.0.1
[info]   | |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-w..
[info]   | |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | |   | +-org.checkerframework:checker-qual:3.37.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by:..
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2...
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (e..
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evict..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | | 
[info]   | |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.lz4:lz4-java:1.8.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-build-link:2.9.1
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | 
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | | 
[info]   | |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | 
[info]   | |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   | |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-javax.inject:javax.inject:1
[info]   | |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   
[info]   | +-com.typesafe.play:shaded-asynchttpclient:2.2.5
[info]   | +-com.typesafe.play:shaded-oauth:2.2.5
[info]   | +-javax.cache:cache-api:1.1.1
[info]   | 
[info]   +-com.typesafe.play:play-akka-http-server_2.13:2.9.1 [S]
[info]   | +-com.typesafe.akka:akka-http-core_2.13:10.2.10 [S]
[info]   | | +-com.typesafe.akka:akka-parsing_2.13:10.2.10 [S]
[info]   | | 
[info]   | +-com.typesafe.play:play-server_2.13:2.9.1 [S]
[info]   | | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | 
[info]   | |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   |   
[info]   | |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | 
[info]   | |   +-com.google.guava:guava:32.1.3-jre
[info]   | |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | |   | +-com.google.guava:failureaccess:1.0.1
[info]   | |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-w..
[info]   | |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | |   | +-org.checkerframework:checker-qual:3.37.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by:..
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2...
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (e..
[info]   | |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evict..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evi..
[info]   | |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | | 
[info]   | |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | |   | | 
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.lz4:lz4-java:1.8.0
[info]   | |   | 
[info]   | |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | +-com.typesafe:config:1.4.3
[info]   | |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | 
[info]   | |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-build-link:2.9.1
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | 
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   | |   
[info]   | |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   | |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | |   | | | 
[info]   | |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | | | +-com.typesafe:config:1.4.3
[info]   | |   | | | 
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | | 
[info]   | |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | |   | 
[info]   | |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   | |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | |   | 
[info]   | |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | |   | +-com.typesafe:config:1.4.3
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   | |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | |   | 
[info]   | |   +-javax.inject:javax.inject:1
[info]   | |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   | |   | +-org.slf4j:slf4j-api:2.0.16
[info]   | |   | 
[info]   | |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | |   +-org.slf4j:slf4j-api:2.0.16
[info]   | |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | |   
[info]   | +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   
[info]   +-com.typesafe.play:play-cache_2.13:2.9.1 [S]
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | 
[info]   |   +-com.google.guava:guava:32.1.3-jre
[info]   |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   |   | +-com.google.guava:failureaccess:1.0.1
[info]   |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   |   | +-org.checkerframework:checker-qual:3.37.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | | 
[info]   |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.lz4:lz4-java:1.8.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.play:play-build-link:2.9.1
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | 
[info]   |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   | 
[info]   |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   | 
[info]   |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | 
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-javax.inject:javax.inject:1
[info]   |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   
[info]   +-com.typesafe.play:play-filters-helpers_2.13:2.9.1 [S]
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | 
[info]   |   +-com.google.guava:guava:32.1.3-jre
[info]   |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   |   | +-com.google.guava:failureaccess:1.0.1
[info]   |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   |   | +-org.checkerframework:checker-qual:3.37.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | | 
[info]   |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.lz4:lz4-java:1.8.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.play:play-build-link:2.9.1
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | 
[info]   |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   | 
[info]   |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   | 
[info]   |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | 
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-javax.inject:javax.inject:1
[info]   |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   
[info]   +-com.typesafe.play:play-jdbc-evolutions_2.13:2.9.1 [S]
[info]   | +-com.typesafe.play:play-jdbc-api_2.13:2.9.1 [S]
[info]   | | +-javax.inject:javax.inject:1
[info]   | | 
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | 
[info]   |   +-com.google.guava:guava:32.1.3-jre
[info]   |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   |   | +-com.google.guava:failureaccess:1.0.1
[info]   |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   |   | +-org.checkerframework:checker-qual:3.37.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | | 
[info]   |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.lz4:lz4-java:1.8.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.play:play-build-link:2.9.1
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | 
[info]   |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   | 
[info]   |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   | 
[info]   |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | 
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-javax.inject:javax.inject:1
[info]   |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   
[info]   +-com.typesafe.play:play-jdbc_2.13:2.9.1 [S]
[info]   | +-com.googlecode.usc:jdbcdslog:1.0.6.2
[info]   | | +-org.slf4j:slf4j-api:1.5.10 (evicted by: 2.0.16)
[info]   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | 
[info]   | +-com.typesafe.play:play-jdbc-api_2.13:2.9.1 [S]
[info]   | | +-javax.inject:javax.inject:1
[info]   | | 
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | 
[info]   | | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | |   
[info]   | | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | 
[info]   | | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | | | 
[info]   | | +-com.google.guava:guava:32.1.3-jre
[info]   | | | +-com.google.code.findbugs:jsr305:3.0.2
[info]   | | | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   | | | +-com.google.guava:failureaccess:1.0.1
[info]   | | | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   | | | +-com.google.j2objc:j2objc-annotations:2.8
[info]   | | | +-org.checkerframework:checker-qual:3.37.0
[info]   | | | 
[info]   | | +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | 
[info]   | | | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | 
[info]   | | | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | | 
[info]   | | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | 
[info]   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | +-com.typesafe:config:1.4.3
[info]   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | 
[info]   | | +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | 
[info]   | | | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   | | | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   | | | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   | | | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | | 
[info]   | | | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   | | | | 
[info]   | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | 
[info]   | | | +-org.lz4:lz4-java:1.8.0
[info]   | | | 
[info]   | | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | +-com.typesafe:config:1.4.3
[info]   | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | 
[info]   | | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | 
[info]   | | +-com.typesafe.play:play-build-link:2.9.1
[info]   | | | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | | | 
[info]   | | +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   | | | +-com.typesafe.play:play-exceptions:2.9.1
[info]   | | | +-com.typesafe:config:1.4.3
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | | | 
[info]   | | +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | 
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | | |   
[info]   | | | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   | | | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | | | 
[info]   | | +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   | | | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   | | | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   | | | | | 
[info]   | | | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   | | | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | | | +-com.typesafe:config:1.4.3
[info]   | | | | | 
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   | | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | | 
[info]   | | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   | | | 
[info]   | | +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   | | | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | | | 
[info]   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   | | | +-com.typesafe:config:1.4.3
[info]   | | | 
[info]   | | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | | +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   | | | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | | | 
[info]   | | +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   | | | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   | | | 
[info]   | | +-javax.inject:javax.inject:1
[info]   | | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | | +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | 
[info]   | | +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | | +-org.slf4j:jul-to-slf4j:2.0.16
[info]   | | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | | 
[info]   | | +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   | | 
[info]   | +-com.zaxxer:HikariCP:5.0.1
[info]   | | +-org.slf4j:slf4j-api:2.0.0-alpha1 (evicted by: 2.0.16)
[info]   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | 
[info]   | +-tyrex:tyrex:1.0.1
[info]   | 
[info]   +-com.typesafe.play:play-logback_2.13:2.9.1 [S]
[info]   | +-ch.qos.logback:logback-classic:1.4.14 (evicted by: 1.5.6)
[info]   | +-ch.qos.logback:logback-classic:1.5.6
[info]   | | +-org.slf4j:slf4j-api:2.0.13 (evicted by: 2.0.16)
[info]   | | 
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | 
[info]   |   +-com.google.guava:guava:32.1.3-jre
[info]   |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   |   | +-com.google.guava:failureaccess:1.0.1
[info]   |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   |   | +-org.checkerframework:checker-qual:3.37.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | | 
[info]   |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.lz4:lz4-java:1.8.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.play:play-build-link:2.9.1
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | 
[info]   |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   | 
[info]   |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   | 
[info]   |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | 
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-javax.inject:javax.inject:1
[info]   |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   
[info]   +-com.typesafe.play:play-server_2.13:2.9.1 [S]
[info]   | +-com.typesafe.play:play_2.13:2.9.1 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | 
[info]   |   +-com.google.guava:guava:32.1.3-jre
[info]   |   | +-com.google.code.findbugs:jsr305:3.0.2
[info]   |   | +-com.google.errorprone:error_prone_annotations:2.21.1
[info]   |   | +-com.google.guava:failureaccess:1.0.1
[info]   |   | +-com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-wit..
[info]   |   | +-com.google.j2objc:j2objc-annotations:2.8
[info]   |   | +-org.checkerframework:checker-qual:3.37.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor-typed_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-serialization-jackson_2.13:2.6.21 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.11.4 (evicted by: 2..
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.11.4 (evicted by: 2.14.3)
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.11.4 (evicted by: 2.14..
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.11.4 (evi..
[info]   |   | +-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.4 (evicted..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.4 (..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.11.4 (evict..
[info]   |   | +-com.fasterxml.jackson.module:jackson-module-scala_2.13:2.14.3 [S]
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | | 
[info]   |   | | +-com.thoughtworks.paranamer:paranamer:2.8
[info]   |   | | 
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.lz4:lz4-java:1.8.0
[info]   |   | 
[info]   |   +-com.typesafe.akka:akka-slf4j_2.13:2.6.21 [S]
[info]   |   | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | +-com.typesafe:config:1.4.3
[info]   |   | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | 
[info]   |   | +-org.slf4j:slf4j-api:1.7.36 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-com.typesafe.play:play-build-link:2.9.1
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | 
[info]   |   +-com.typesafe.play:play-configuration_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.play:play-exceptions:2.9.1
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   | 
[info]   |   +-com.typesafe.play:play-json_2.13:2.10.3 [S]
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | 
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | |   
[info]   |   | +-com.typesafe.play:play-functional_2.13:2.10.3 [S]
[info]   |   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   | 
[info]   |   +-com.typesafe.play:play-streams_2.13:2.9.1 [S]
[info]   |   | +-com.typesafe.akka:akka-stream_2.13:2.6.21 [S]
[info]   |   | | +-com.typesafe.akka:akka-actor_2.13:2.6.21 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | +-org.scala-lang.modules:scala-java8-compat_2.13:1.0.0 [S]
[info]   |   | | | 
[info]   |   | | +-com.typesafe.akka:akka-protobuf-v3_2.13:2.6.21
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.4.3 (evicted by: 0.6.1)
[info]   |   | | +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | | | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | | | +-com.typesafe:config:1.4.3
[info]   |   | | | 
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.3 (evicted by: 1.0.4)
[info]   |   | | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | | 
[info]   |   | +-org.reactivestreams:reactive-streams:1.0.4
[info]   |   | 
[info]   |   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   |   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   |   | 
[info]   |   +-com.typesafe:ssl-config-core_2.13:0.6.1 [S]
[info]   |   | +-com.typesafe:config:1.4.2 (evicted by: 1.4.3)
[info]   |   | +-com.typesafe:config:1.4.3
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   +-io.jsonwebtoken:jjwt-impl:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-io.jsonwebtoken:jjwt-jackson:0.11.5
[info]   |   | +-io.jsonwebtoken:jjwt-api:0.11.5
[info]   |   | 
[info]   |   +-javax.inject:javax.inject:1
[info]   |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jcl-over-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.16
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.slf4j:jul-to-slf4j:2.0.9 (evicted by: 2.0.16)
[info]   |   +-org.slf4j:slf4j-api:2.0.16
[info]   |   +-org.slf4j:slf4j-api:2.0.9 (evicted by: 2.0.16)
[info]   |   
[info]   +-com.typesafe.play:twirl-api_2.13:1.6.4 [S]
[info]   | +-org.scala-lang.modules:scala-xml_2.13:2.2.0 [S]
[info]   | 
[info]   +-commons-validator:commons-validator:1.6
[info]   | +-commons-beanutils:commons-beanutils:1.9.2
[info]   | | +-commons-collections:commons-collections:3.2.1 (evicted by: 3.2.2)
[info]   | | +-commons-collections:commons-collections:3.2.2
[info]   | | 
[info]   | +-commons-collections:commons-collections:3.2.2
[info]   | +-commons-digester:commons-digester:1.8.1
[info]   | 
[info]   +-de.svenkubiak:jBCrypt:0.4.1
[info]   +-org.jsoup:jsoup:1.15.4
[info]   +-org.playframework:play-json-joda_2.13:3.0.4 [S]
[info]   | +-joda-time:joda-time:2.12.7
[info]   | +-org.playframework:play-json_2.13:3.0.4 [S]
[info]   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | 
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   |   |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   |   |   
[info]   |   +-org.playframework:play-functional_2.13:3.0.4 [S]
[info]   |   +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |   
[info]   +-org.playframework:play-json_2.13:3.0.4 [S]
[info]   | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | 
[info]   | +-com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   
[info]   | +-com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | | +-com.fasterxml.jackson.core:jackson-databind:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-annotations:2.14.3
[info]   | |   +-com.fasterxml.jackson.core:jackson-core:2.14.3
[info]   | |   
[info]   | +-org.playframework:play-functional_2.13:3.0.4 [S]
[info]   | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   | 
[info]   +-org.postgresql:postgresql:42.2.5
[info]   +-org.scalikejdbc:scalikejdbc-config_2.13:3.3.5 [S]
[info]   | +-com.typesafe:config:1.3.4 (evicted by: 1.4.3)
[info]   | +-com.typesafe:config:1.4.3
[info]   | +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]   | | +-org.apache.commons:commons-dbcp2:2.5.0
[info]   | | | +-org.apache.commons:commons-pool2:2.6.0
[info]   | | | 
[info]   | | +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]   | | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   | | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   | | +-org.slf4j:slf4j-api:2.0.16
[info]   | | 
[info]   | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   | +-org.slf4j:slf4j-api:2.0.16
[info]   | 
[info]   +-org.scalikejdbc:scalikejdbc-joda-time_2.13:3.3.5 [S]
[info]   | +-joda-time:joda-time:2.10.1 (evicted by: 2.12.7)
[info]   | +-joda-time:joda-time:2.12.7
[info]   | +-org.joda:joda-convert:2.2.0
[info]   | +-org.scalikejdbc:scalikejdbc_2.13:3.3.5 [S]
[info]   |   +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]   |   | +-org.apache.commons:commons-dbcp2:2.5.0
[info]   |   | | +-org.apache.commons:commons-pool2:2.6.0
[info]   |   | | 
[info]   |   | +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]   |   | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |   | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   |   | +-org.slf4j:slf4j-api:2.0.16
[info]   |   | 
[info]   |   +-org.scalikejdbc:scalikejdbc-interpolation_2.13:3.3.5 [S]
[info]   |     +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]   |     | +-org.apache.commons:commons-dbcp2:2.5.0
[info]   |     | | +-org.apache.commons:commons-pool2:2.6.0
[info]   |     | | 
[info]   |     | +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]   |     | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |     | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   |     | +-org.slf4j:slf4j-api:2.0.16
[info]   |     | 
[info]   |     +-org.scalikejdbc:scalikejdbc-interpolation-macro_2.13:3.3.5 [S]
[info]   |     | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]   |     | +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]   |     |   +-org.apache.commons:commons-dbcp2:2.5.0
[info]   |     |   | +-org.apache.commons:commons-pool2:2.6.0
[info]   |     |   | 
[info]   |     |   +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]   |     |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]   |     |   +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   |     |   +-org.slf4j:slf4j-api:2.0.16
[info]   |     |   
[info]   |     +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]   |     +-org.slf4j:slf4j-api:2.0.16
[info]   |     
[info]   +-org.scalikejdbc:scalikejdbc-play-initializer_2.13:2.8.0-scalikejdbc-3.5 [S]
[info]   +-org.scalikejdbc:scalikejdbc_2.13:3.3.5 [S]
[info]     +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]     | +-org.apache.commons:commons-dbcp2:2.5.0
[info]     | | +-org.apache.commons:commons-pool2:2.6.0
[info]     | | 
[info]     | +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]     | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]     | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]     | +-org.slf4j:slf4j-api:2.0.16
[info]     | 
[info]     +-org.scalikejdbc:scalikejdbc-interpolation_2.13:3.3.5 [S]
[info]       +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]       | +-org.apache.commons:commons-dbcp2:2.5.0
[info]       | | +-org.apache.commons:commons-pool2:2.6.0
[info]       | | 
[info]       | +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]       | +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]       | +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]       | +-org.slf4j:slf4j-api:2.0.16
[info]       | 
[info]       +-org.scalikejdbc:scalikejdbc-interpolation-macro_2.13:3.3.5 [S]
[info]       | +-org.scala-lang:scala-reflect:2.13.15 [S]
[info]       | +-org.scalikejdbc:scalikejdbc-core_2.13:3.3.5 [S]
[info]       |   +-org.apache.commons:commons-dbcp2:2.5.0
[info]       |   | +-org.apache.commons:commons-pool2:2.6.0
[info]       |   | 
[info]       |   +-org.scala-lang.modules:scala-collection-compat_2.13:2.0.0 [S]
[info]       |   +-org.scala-lang.modules:scala-parser-combinators_2.13:1.1.2 [S]
[info]       |   +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]       |   +-org.slf4j:slf4j-api:2.0.16
[info]       |   
[info]       +-org.slf4j:slf4j-api:1.7.25 (evicted by: 2.0.16)
[info]       +-org.slf4j:slf4j-api:2.0.16
[info]       
[success] Total time: 3 s, completed 09-Sept-2025, 5:34:23 pm
