<!-- https://www.playframework.com/documentation/latest/SettingsLogger -->
<configuration>

    <conversionRule conversionWord="coloredLevel" converterClass="play.api.libs.logback.ColoredLevel"/>

<!--    <appender name="FILE" class="ch.qos.logback.core.FileAppender">-->
<!--        <file>${application.home:-.}/logs/application.log</file>-->
<!--        <encoder>-->
<!--            <pattern>%date [%level] from %logger in %thread - %message%n%xException</pattern>-->
<!--        </encoder>-->
<!--    </appender>-->

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%coloredLevel %logger{15} - %message%n%xException{10}</pattern>
        </encoder>
    </appender>

<!--    <appender name="loggly" class="ch.qos.logback.ext.loggly.LogglyAppender">-->
<!--        <endpointUrl>https://logs-01.loggly.com/inputs/************************************/tag/coldemail-local-->
<!--        </endpointUrl>-->
<!--        <pattern>%m%n</pattern>-->
<!--    </appender>-->

    <!-- REF: https://www.baeldung.com/java-logging-rolling-file-appenders#4-rolling-based-on-size-and-time-1-->
    <!--<appender name="roll-by-time-and-size"-->
              <!--class="ch.qos.logback.core.rolling.RollingFileAppender">-->
        <!--<file>/home/<USER>/srlogs/sr_app.log</file>-->
        <!--<rollingPolicy-->
                <!--class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
            <!--<fileNamePattern>-->
                <!--/home/<USER>/srlogs/sr_app.%d{yyyy-MM-dd}.%i.gz-->
            <!--</fileNamePattern>-->
            <!--<maxFileSize>10MB</maxFileSize>-->
            <!--<maxHistory>30</maxHistory>-->
            <!--<totalSizeCap>500MB</totalSizeCap>-->
        <!--</rollingPolicy>-->
        <!--<encoder>-->
            <!--<pattern>%d{yyyy-MM-dd HH:mm:ss} %p %m%n</pattern>-->
        <!--</encoder>-->
    <!--</appender>-->

<!--    <appender name="ASYNCFILE" class="ch.qos.logback.classic.AsyncAppender">-->
<!--        <appender-ref ref="FILE"/>-->
<!--    </appender>-->

    <!-- REF: https://cloud.google.com/logging/docs/setup/java-->
    <appender name="gcloud" class="com.google.cloud.logging.logback.LoggingAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <flushLevel>WARN</flushLevel> <!-- Optional : default ERROR -->
    </appender>

    <appender name="ASYNCSTDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT"/>
    </appender>

    <logger name="play" level="INFO"/>
    <logger name="application" level="DEBUG"/>

    <!-- Off these ones as they are annoying, and anyway we manage configuration ourselves -->
    <logger name="com.avaje.ebean.config.PropertyMapLoader" level="OFF"/>
    <logger name="com.avaje.ebeaninternal.server.core.XmlConfigLoader" level="OFF"/>
    <logger name="com.avaje.ebeaninternal.server.lib.BackgroundThread" level="OFF"/>
    <logger name="com.gargoylesoftware.htmlunit.javascript" level="OFF"/>

    <root level="INFO">
<!--        <appender-ref ref="ASYNCFILE"/>-->
        <appender-ref ref="ASYNCSTDOUT"/>
<!--        <appender-ref ref="loggly"/>-->
<!--        <appender-ref ref="roll-by-time-and-size"/>-->
<!--        <appender-ref ref="gcloud"/>-->
    </root>

</configuration>
