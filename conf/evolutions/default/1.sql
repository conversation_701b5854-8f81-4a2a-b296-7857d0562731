# --- !Ups

-- CREATE TYPE folder_type_enum AS ENUM (
--   	'inbox',
--   	'spam',
--   	'category',
--   	'bounced',
--   	'landing_check_failed'
--   );


CREATE TABLE IF NOT EXISTS accounts
(
    id BIGSERIAL PRIMARY KEY,
    email text NOT NULL UNIQUE,
    password text NOT NULL,
    first_name text,
    last_name text,
    sr_api_key text,
    updated_at TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    active boolean DEFAULT true,
    email_verified boolean DEFAULT false,
    email_verification_code text,
    email_verification_code_created_at TIMESTAMPTZ,
    total_allowed_warmup_emails integer NOT NULL DEFAULT 0,
    last_sr_credits_rest_at TIMESTAMPTZ);

# --- !Downs

DROP TABLE accounts;