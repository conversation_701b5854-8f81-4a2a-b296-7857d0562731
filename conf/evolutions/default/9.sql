# --- !Ups

AL<PERSON>R TABLE accounts
   ADD COLUMN IF NOT EXISTS sr_api_key_error TEXT;


ALTER TABLE accounts
   ADD COLUMN IF NOT EXISTS total_allowed_warmup_emails integer NOT NULL DEFAULT 0;

ALTER TABLE accounts
   ADD COLUMN IF NOT EXISTS last_sr_credits_rest_at TIMESTAMPTZ;


# --- !Downs

ALTER TABLE accounts
   DROP COLUMN IF EXISTS sr_api_key_error;

ALTER TABLE accounts
   DROP COLUMN IF EXISTS total_allowed_warmup_emails;

ALTER TABLE accounts
   DROP COLUMN IF EXISTS last_sr_credits_rest_at;